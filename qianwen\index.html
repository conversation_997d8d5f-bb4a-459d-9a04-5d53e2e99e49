<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Triptopia - Discover the World</title>
    <link rel="stylesheet" href="./styles.css" />
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css"
    />
  </head>
  <body>
    <!-- Header -->
    <header class="bg-white shadow-md">
      <div class="container mx-auto px-4 py-3 flex justify-between items-center">
        <a href="#" class="text-xl font-bold text-green-600">Triptopia</a>
        <nav>
          <ul class="flex space-x-4">
            <li><a href="#" class="text-orange-500 hover:text-orange-700">Home</a></li>
            <li><a href="#" class="text-gray-600 hover:text-gray-800">Blog</a></li>
            <li><a href="#" class="text-gray-600 hover:text-gray-800">Destination</a></li>
            <li><a href="#" class="text-gray-600 hover:text-gray-800">Pages</a></li>
            <li><a href="#" class="text-gray-600 hover:text-gray-800">Contact</a></li>
          </ul>
        </nav>
        <button class="bg-green-600 text-white px-4 py-2 rounded">Book Trip</button>
      </div>
    </header>

    <!-- Hero Section -->
    <section
      class="bg-cover bg-center h-screen flex items-center justify-center"
      style="background-image: url('https://placehold.co/1920x1080 ')"
    >
      <div class="text-center">
        <h1 class="text-5xl font-bold text-green-800 mb-4"
          >Discover the world, one adventure at a time</h1
        >
        <p class="text-lg text-gray-600 mb-6"
          >Experience bespoke itineraries and unparalleled service. Explore the world without
          breaking the bank.</p
        >
        <div class="flex justify-center space-x-4">
          <button class="bg-green-800 text-white px-4 py-2 rounded">Contact Us</button>
          <button class="bg-white border border-green-800 text-green-800 px-4 py-2 rounded"
            >Get Quote</button
          >
        </div>
      </div>
    </section>

    <!-- Search Section -->
    <section
      class="bg-cover bg-center py-16"
      style="background-image: url('https://placehold.co/1920x1080 ')"
    >
      <div class="container mx-auto px-4 relative">
        <div class="absolute inset-0 bg-black opacity-50"></div>
        <div class="relative z-10 flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4">
          <div class="w-full md:w-1/4">
            <label for="destination" class="block text-sm font-medium text-white"
              >Destination</label
            >
            <select id="destination" class="w-full p-2 border border-gray-300 rounded">
              <option value="istanbul">Istanbul</option>
              <option value="paris">Paris</option>
              <option value="tokyo">Tokyo</option>
            </select>
          </div>
          <div class="w-full md:w-1/4">
            <label for="duration" class="block text-sm font-medium text-white">Duration</label>
            <select id="duration" class="w-full p-2 border border-gray-300 rounded">
              <option value="4">4 Days</option>
              <option value="5">5 Days</option>
              <option value="7">7 Days</option>
            </select>
          </div>
          <div class="w-full md:w-1/4">
            <label for="travel-type" class="block text-sm font-medium text-white"
              >Travel Type</label
            >
            <select id="travel-type" class="w-full p-2 border border-gray-300 rounded">
              <option value="adventure">Adventure</option>
              <option value="luxury">Luxury</option>
              <option value="budget">Budget</option>
            </select>
          </div>
          <div class="w-full md:w-1/4">
            <label for="travellers" class="block text-sm font-medium text-white">Travellers</label>
            <select id="travellers" class="w-full p-2 border border-gray-300 rounded">
              <option value="1">1</option>
              <option value="2">2</option>
              <option value="4">4</option>
            </select>
          </div>
          <button class="bg-yellow-500 text-white px-4 py-2 rounded">Find Availability</button>
        </div>
      </div>
    </section>

    <!-- About Section -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-4 grid grid-cols-1 md:grid-cols-2 gap-8">
        <div>
          <h2 class="text-3xl font-bold text-green-800 mb-4"
            >Experience the World With Triptopia</h2
          >
          <p class="text-gray-600 mb-4"
            >Triptopia was born out of a shared passion for exploration and a desire to create
            meaningful travel experiences. We believe travel is more than just visiting a place;
            it's about understanding cultures and connecting with people.</p
          >
          <p class="text-gray-600 mb-4"
            >The world is a book and those who do not travel read only one page.</p
          >
          <ul class="list-disc pl-5 text-gray-600">
            <li>To curate personalized itineraries</li>
            <li>We believe travel fosters understanding</li>
          </ul>
          <button class="bg-yellow-500 text-white px-4 py-2 rounded mt-4">Discover More</button>
        </div>
        <div>
          <img
            src="https://placehold.co/600x800 "
            alt="Traveler Exploring"
            class="w-full h-full object-cover rounded"
          />
        </div>
      </div>
    </section>

    <!-- Destinations Section -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-4">
        <h2 class="text-3xl font-bold text-center text-green-800 mb-8"
          >Search a best place in the world</h2
        >
        <p class="text-gray-600 text-center mb-8"
          >Our platform offers a range of features and benefits that make planning your dream trip
          simple and enjoyable. With our advanced matching algorithm, you can find the perfect
          destination tailored to your preferences.</p
        >
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div class="bg-white p-4 rounded shadow">
            <img
              src="https://placehold.co/300x200 "
              alt="Batu, East Java"
              class="w-full h-40 object-cover rounded mb-2"
            />
            <h3 class="text-xl font-bold text-green-800">Batu, East Java</h3>
            <p class="text-gray-600">86 Destinations</p>
          </div>
          <!-- Repeat similar blocks for other destinations -->
        </div>
      </div>
    </section>

    <!-- Team Section -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-4">
        <h2 class="text-3xl font-bold text-center text-green-800 mb-8"
          >Meet the Creative Minds Behind Triptopia</h2
        >
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="bg-white p-4 rounded shadow flex flex-col items-center">
            <img
              src="https://placehold.co/200x200 "
              alt="Vasili Ilmaz"
              class="w-32 h-32 rounded-full mb-2"
            />
            <h3 class="text-xl font-bold text-green-800">Vasili Ilmaz</h3>
            <p class="text-gray-600">Web Developer</p>
          </div>
          <!-- Repeat similar blocks for other team members -->
        </div>
      </div>
    </section>

    <!-- Testimonials Section -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-4">
        <h2 class="text-3xl font-bold text-center text-green-800 mb-8"
          >What Our Traveller Said About Us</h2
        >
        <div class="carousel relative overflow-hidden">
          <div class="carousel-inner flex transition-transform duration-500 ease-in-out">
            <div class="carousel-item w-full p-4 bg-white rounded shadow">
              <p class="text-gray-600 mb-2"
                >I can't thank Triptopia enough for helping me find love! Initially, I was hesitant
                about online dating, but this platform changed my perspective entirely. The
                user-friendly interface, personalized matches, and the welcoming community made me
                feel at ease from the very beginning.</p
              >
              <div class="flex items-center justify-between">
                <div class="flex items-center">
                  <img
                    src="https://placehold.co/50x50 "
                    alt="User Profile"
                    class="w-10 h-10 rounded-full mr-2"
                  />
                  <div>
                    <p class="text-gray-600">Berk Ariso</p>
                    <p class="text-gray-400 text-sm">Product Designer</p>
                  </div>
                </div>
              </div>
            </div>
            <!-- Repeat similar blocks for other testimonials -->
          </div>
          <button
            class="carousel-prev absolute top-1/2 left-0 transform -translate-y-1/2 bg-white p-2 rounded-full focus:outline-none"
            onclick="prevSlide()"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15 19l-7-7 7-7"
              />
            </svg>
          </button>
          <button
            class="carousel-next absolute top-1/2 right-0 transform -translate-y-1/2 bg-white p-2 rounded-full focus:outline-none"
            onclick="nextSlide()"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 5l7 7-7 7"
              />
            </svg>
          </button>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="bg-white py-8">
      <div class="container mx-auto px-4 flex flex-col md:flex-row justify-between items-center">
        <div class="mb-4 md:mb-0">
          <img src="https://placehold.co/100x50 " alt="Triptopia Logo" class="w-32 h-8" />
          <p class="text-gray-600 mt-2"
            >Hello, we are Triptopia. Our goal is to translate the positive effects from
            revolutionizing travel into real-world impact.</p
          >
          <div class="flex space-x-2 mt-2">
            <a href="#"><i class="fab fa-facebook-square text-gray-600"></i></a>
            <a href="#"><i class="fab fa-twitter-square text-gray-600"></i></a>
            <a href="#"><i class="fab fa-instagram-square text-gray-600"></i></a>
            <a href="#"><i class="fab fa-linkedin-square text-gray-600"></i></a>
          </div>
        </div>
        <div class="mb-4 md:mb-0">
          <h3 class="text-xl font-bold text-green-800 mb-2">About</h3>
          <ul class="text-gray-600">
            <li><a href="#" class="hover:text-green-600">About Us</a></li>
            <li><a href="#" class="hover:text-green-600">Our Services</a></li>
            <li><a href="#" class="hover:text-green-600">Privacy Policy</a></li>
            <li><a href="#" class="hover:text-green-600">Terms & Conditions</a></li>
          </ul>
        </div>
        <div class="mb-4 md:mb-0">
          <h3 class="text-xl font-bold text-green-800 mb-2">Contact</h3>
          <ul class="text-gray-600">
            <li><i class="fas fa-phone mr-2"></i> +012 345 67890</li>
            <li><i class="fas fa-envelope mr-2"></i> <EMAIL></li>
            <li><i class="fas fa-map-marker-alt mr-2"></i> 123 Street, New York, USA</li>
          </ul>
        </div>
        <div>
          <h3 class="text-xl font-bold text-green-800 mb-2">Gallery</h3>
          <div class="grid grid-cols-3 gap-2">
            <img src="https://placehold.co/100x100 " alt="Gallery Image" class="rounded" />
            <!-- Repeat similar blocks for other gallery images -->
          </div>
        </div>
      </div>
      <hr class="my-4" />
      <div class="text-center text-gray-600">© 2024 Triptopia. All rights reserved.</div>
    </footer>

    <script src="./script.js"></script>
  </body>
</html>
