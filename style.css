body, html {
    margin: 0;
    padding: 0;
    height: 100%;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Aria<PERSON>, sans-serif;
    background-color: #f2f2f2;
}

.container {
    width: 100%;
    max-width: 480px;
    height: 100%;
    margin: 0 auto;
    background-color: #ffffff;
    display: flex;
    flex-direction: column;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.header {
    display: flex;
    align-items: center;
    padding: 15px;
    background-color: #f7f7f7;
    border-bottom: 1px solid #e5e5e5;
}

.header .close-btn {
    font-size: 24px;
    color: #888;
    cursor: pointer;
}

.header .title {
    flex-grow: 1;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    color: #333;
}

.merchant-info {
    text-align: center;
    padding: 40px 20px;
    border-bottom: 1px solid #e5e5e5;
}

.merchant-info .merchant-name {
    font-size: 16px;
    color: #555;
    margin: 0 0 15px;
}

.merchant-info .amount {
    font-size: 40px;
    font-weight: 700;
    color: #333;
    margin: 0;
}

.payment-details {
    padding: 20px;
    flex-grow: 1;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    font-size: 16px;
    color: #333;
    border-bottom: 1px solid #f2f2f2;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-item .label {
    color: #888;
}

.detail-item .value {
    display: flex;
    align-items: center;
}

.detail-item .change-method {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-top: 2px solid #ccc;
    border-right: 2px solid #ccc;
    transform: rotate(45deg);
    margin-left: 10px;
}

.pay-button-container {
    padding: 20px;
}

.pay-button {
    width: 100%;
    padding: 15px;
    font-size: 18px;
    font-weight: 600;
    color: #fff;
    background-color: #07c160;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.pay-button:hover {
    background-color: #06ad56;
}

.pay-button:active {
    background-color: #05994c;
}

.footer {
    text-align: center;
    padding: 20px;
    font-size: 12px;
    color: #aaa;
} 