<view class="container">
  <!-- 相机预览区域 -->
  <camera 
    wx:if="{{!hideCamera}}"
    device-position="front"
    flash="off"
    binderror="handleError"
    class="camera">
  </camera>

  <!-- 状态显示区域 -->
  <view class="status-area">
    <block wx:if="{{detecting}}">
      <view class="status-text">请自然眨眼，正在检测第{{blinkCount}}次眨眼</view>
      <view class="timer" wx:if="{{showTimer}}">{{currentTimer}}s</view>
    </block>
    <block wx:else>
      <view class="status-text">{{statusText}}</view>
      <view class="interval-text" wx:if="{{showInterval}}">
        检测到的最大眨眼间隔时间：{{maxInterval}}秒
      </view>
    </block>
  </view>

  <!-- 错误提示 -->
  <view wx:if="{{errorTips}}" class="error-tips">
    {{errorTips}}
  </view>

  <!-- 操作按钮 -->
  <view class="button-area">
    <button 
      class="btn"
      hover-class="btn-hover"
      bindtap="startDetection"
      wx:if="{{!detecting}}">
      {{buttonText}}
    </button>
  </view>
</view>