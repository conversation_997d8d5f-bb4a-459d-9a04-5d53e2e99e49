<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>打印次数记录</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
    }
    #print-count {
      font-weight: bold;
    }
  </style>
</head>
<body>
  <h1>打印次数记录示例</h1>
  <p>总打印次数: <span id="print-count">0</span></p>
  <p>这是一个示例页面，用于演示如何记录用户的打印次数。</p>
  <p>当你点击浏览器的打印功能并完成打印后，打印次数会增加并显示在这里。</p>

  <script>
    // 初始化打印次数计数器
    let printCount = 0;

    // 监听打印前事件
    window.addEventListener('beforeprint', function(event) {
      console.log('用户即将打印');
      // 在这里可以执行一些操作，比如记录日志或更改页面内容
    });

    // 监听打印后事件
    window.addEventListener('afterprint', function(event) {
      console.log('用户已经完成打印');
      // 增加打印次数
      printCount++;
      console.log('总打印次数:', printCount);
      // 在这里可以执行一些操作，比如重置页面内容或更新状态
    });

    // 示例：显示打印次数
    function displayPrintCount() {
      const countElement = document.getElementById('print-count');
      if (countElement) {
        countElement.textContent = `${printCount}`;
      }
    }

    // 每次打印后更新显示
    window.addEventListener('afterprint', displayPrintCount);

    // 初始化显示
    displayPrintCount();
  </script>
</body>
</html>