<template>
  <view class="input-number">
    <button class="btn" @click="decrease" :disabled="currentValue <= min">-</button>
    <input
      class="input"
      type="number"
      v-model="currentValue"
      @input="onInput"
      :min="min"
      :max="max"
    />
    <button class="btn" @click="increase" :disabled="currentValue >= max">+</button>
  </view>
</template>

<script>
  export default {
    name: 'InputNumber',
    props: {
      value: {
        type: Number,
        default: 0,
      },
      min: {
        type: Number,
        default: 0,
      },
      max: {
        type: Number,
      },
      step: {
        type: Number,
        default: 1,
      },
      decimal: {
        type: Number,
        default: 0,
      },
    },
    data() {
      return {
        currentValue: this.value,
      };
    },
    watch: {
      value(val) {
        console.log('val: ', val);
        this.currentValue = val;
      },
    },
    methods: {
      increase() {
        let val = this.currentValue + this.step;
        if (val > this.max) val = this.max;
        val = this.limitDecimal(val);
        this.updateValue(val);
      },
      decrease() {
        let val = this.currentValue - this.step;
        if (val < this.min) val = this.min;
        val = this.limitDecimal(val);
        this.updateValue(val);
      },
      onInput(e) {
        let val = Number(e.detail.value || e.target.value);
        if (isNaN(val)) val = this.min;
        if (val > this.max) val = this.max;
        if (val < this.min) val = this.min;
        val = this.limitDecimal(val);
        this.updateValue(val);
      },
      updateValue(val) {
        this.$nextTick(() => {
          this.currentValue = val;
          this.$emit('input', val);
        });
      },
      limitDecimal(val) {
        if (typeof val === 'number' && this.decimal >= 0) {
          const factor = Math.pow(10, this.decimal);
          return Math.round(val * factor) / factor;
        }
        return val;
      },
    },
  };
</script>

<style scoped>
  .input-number {
    display: flex;
    align-items: center;
  }
  .btn {
    width: 40rpx;
    height: 40rpx;
    text-align: center;
  }
  .input {
    width: 280rpx;
    text-align: center;
    margin: 0 10rpx;
  }
</style>
