const http = require('http');
const fs = require('fs');
const path = require('path');

const server = http.createServer((req, res) => {
    // 只处理下载请求路径
    if (req.url === '/download') {
        // 示例：下载1.html文件
        const filePath = path.join(__dirname, '1.json');

        // 检查文件是否存在
        fs.access(filePath, fs.constants.F_OK, (err) => {
            if (err) {
                res.writeHead(404, { 'Content-Type': 'text/plain' });
                res.end('文件不存在');
                return;
            }

            // 获取文件信息
            fs.stat(filePath, (err, stats) => {
                if (err) {
                    res.writeHead(500, { 'Content-Type': 'text/plain' });
                    res.end('服务器错误');
                    return;
                }

                // 设置响应头，包括CORS配置
                res.writeHead(200, {
                    'Content-Type': 'application/octet-stream',
                    'Content-Disposition': 'attachment; filename="' + path.basename(filePath) + '"',
                    'Content-Length': stats.size,
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'GET, OPTIONS',
                    'Access-Control-Allow-Headers': 'Content-Type'
                });

                // 创建文件读取流并通过管道传输到响应对象
                const fileStream = fs.createReadStream(filePath);
                fileStream.pipe(res);

                // 错误处理
                fileStream.on('error', (err) => {
                    res.writeHead(500, { 'Content-Type': 'text/plain' });
                    res.end('文件读取错误');
                });
            });
        });
    } else {
        res.writeHead(404, { 'Content-Type': 'text/plain' });
        res.end('无效的请求路径');
    }
});

const PORT = 3000;
server.listen(PORT, () => {
    console.log(`服务器运行在 http://localhost:${PORT}`);
    console.log(`访问 http://localhost:${PORT}/download 来下载文件`);
});