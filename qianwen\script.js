// Carousel functionality
let carousel = document.querySelector('.carousel-inner');
let currentIndex = 0;
const slides = Array.from(carousel.children);

function prevSlide() {
  currentIndex = (currentIndex - 1 + slides.length) % slides.length;
  updateCarousel();
}

function nextSlide() {
  currentIndex = (currentIndex + 1) % slides.length;
  updateCarousel();
}

function updateCarousel() {
  carousel.style.transform = `translateX(-${currentIndex * 100}%)`;
}