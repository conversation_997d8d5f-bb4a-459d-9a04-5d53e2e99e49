/*!
 * Country data
 * Date: 2022-07-08T15:06:11+08:00
 */

(function (factory) {
  if (typeof define === 'function' && define.amd) {
    // AMD. Register as anonymous module.
    define('countries', [], factory);
  } else {
    // Browser globals.
    factory();
  }
})(function () {
  var countries = {
    AF: 'Afghanistan',
    AL: 'Albania',
    DZ: 'Algeria',
    AS: 'American Samoa',
    AD: 'Andorra',
    AO: 'Angola',
    AI: 'Anguilla',
    AG: 'Antigua and Barbuda',
    AR: 'Argentina',
    AM: 'Armenia',
    AW: 'Aruba',
    AU: 'Australia',
    AT: 'Austria',
    AZ: 'Azerbaijan',
    BS: 'Bahamas',
    BH: 'Bahrain',
    BD: 'Bangladesh',
    BB: 'Barbados',
    BY: 'Belarus',
    BE: 'Belgium',
    BZ: 'Belize',
    BJ: 'Benin',
    BM: 'Bermuda',
    BT: 'Bhutan',
    BO: 'Bolivia, Plurinational State of',
    BA: 'Bosnia and Herzegovina',
    BW: 'Botswana',
    BV: 'Bouvet Island',
    BR: 'Brazil',
    IO: 'British Indian Ocean Territory',
    BN: 'Brunei Darussalam',
    BG: 'Bulgaria',
    BF: 'Burkina Faso',
    BI: 'Burundi',
    KH: 'Cambodia',
    CM: 'Cameroon',
    CA: 'Canada',
    CV: 'Cape Verde',
    KY: 'Cayman Islands',
    CF: 'Central African Republic',
    TD: 'Chad',
    CL: 'Chile',
    CN: 'China',
    CO: 'Colombia',
    KM: 'Comoros',
    CG: 'Congo',
    CD: 'Congo, the Democratic Republic of the',
    CK: 'Cook Islands',
    CR: 'Costa Rica',
    CI: 'C' + '&ocirc;' + "te d'Ivoire",
    HR: 'Croatia',
    CU: 'Cuba',
    CW: 'Cura' + '&ccedil;' + 'ao',
    CY: 'Cyprus',
    CZ: 'Czech Republic',
    DK: 'Denmark',
    DJ: 'Djibouti',
    DM: 'Dominica',
    DO: 'Dominican Republic',
    EC: 'Ecuador',
    EG: 'Egypt',
    SV: 'El Salvador',
    GQ: 'Equatorial Guinea',
    ER: 'Eritrea',
    EE: 'Estonia',
    ET: 'Ethiopia',
    FK: 'Falkland Islands (Malvinas)',
    FO: 'Faroe Islands',
    FJ: 'Fiji',
    FI: 'Finland',
    FR: 'France',
    GF: 'French Guiana',
    PF: 'French Polynesia',
    TF: 'French Southern Territories',
    GA: 'Gabon',
    GM: 'Gambia',
    GE: 'Georgia',
    DE: 'Germany',
    GH: 'Ghana',
    GI: 'Gibraltar',
    GR: 'Greece',
    GL: 'Greenland',
    GD: 'Grenada',
    GP: 'Guadeloupe',
    GU: 'Guam',
    GT: 'Guatemala',
    GG: 'Guernsey',
    GN: 'Guinea',
    GW: 'Guinea-Bissau',
    GY: 'Guyana',
    HT: 'Haiti',
    HM: 'Heard Island and McDonald Islands',
    VA: 'Holy See (Vatican City State)',
    HN: 'Honduras',
    HK: 'Hong Kong China',
    HU: 'Hungary',
    IS: 'Iceland',
    IN: 'India',
    ID: 'Indonesia',
    IR: 'Iran, Islamic Republic of',
    IQ: 'Iraq',
    IE: 'Ireland',
    IM: 'Isle of Man',
    IL: 'Israel',
    IT: 'Italy',
    JM: 'Jamaica',
    JP: 'Japan',
    JE: 'Jersey',
    JO: 'Jordan',
    KZ: 'Kazakhstan',
    KE: 'Kenya',
    KI: 'Kiribati',
    KP: "Korea, Democratic People's Republic of",
    KR: 'Korea, Republic of',
    KW: 'Kuwait',
    KG: 'Kyrgyzstan',
    LA: "Lao People's Democratic Republic",
    LV: 'Latvia',
    LB: 'Lebanon',
    LS: 'Lesotho',
    LR: 'Liberia',
    LY: 'Libya',
    LI: 'Liechtenstein',
    LT: 'Lithuania',
    LU: 'Luxembourg',
    MO: 'Macao China',
    MK: 'Macedonia, the former Yugoslav Republic of',
    MG: 'Madagascar',
    MW: 'Malawi',
    MY: 'Malaysia',
    MV: 'Maldives',
    ML: 'Mali',
    MT: 'Malta',
    MH: 'Marshall Islands',
    MQ: 'Martinique',
    MR: 'Mauritania',
    MU: 'Mauritius',
    YT: 'Mayotte',
    MX: 'Mexico',
    FM: 'Micronesia, Federated States of',
    MD: 'Moldova, Republic of',
    MC: 'Monaco',
    MN: 'Mongolia',
    ME: 'Montenegro',
    MS: 'Montserrat',
    MA: 'Morocco',
    MZ: 'Mozambique',
    MM: 'Myanmar',
    NA: 'Namibia',
    NR: 'Nauru',
    NP: 'Nepal',
    NL: 'Netherlands',
    NC: 'New Caledonia',
    NZ: 'New Zealand',
    NI: 'Nicaragua',
    NE: 'Niger',
    NG: 'Nigeria',
    NU: 'Niue',
    NF: 'Norfolk Island',
    MP: 'Northern Mariana Islands',
    NO: 'Norway',
    OM: 'Oman',
    PK: 'Pakistan',
    PW: 'Palau',
    PS: 'Palestinian Territory, Occupied',
    PA: 'Panama',
    PG: 'Papua New Guinea',
    PY: 'Paraguay',
    PE: 'Peru',
    PH: 'Philippines',
    PN: 'Pitcairn',
    PL: 'Poland',
    PT: 'Portugal',
    PR: 'Puerto Rico',
    QA: 'Qatar',
    RE: 'R' + '&eacute;' + 'union',
    RO: 'Romania',
    RU: 'Russian Federation',
    RW: 'Rwanda',
    SH: 'Saint Helena, Ascension and Tristan da Cunha',
    KN: 'Saint Kitts and Nevis',
    LC: 'Saint Lucia',
    MF: 'Saint Martin (French part)',
    PM: 'Saint Pierre and Miquelon',
    VC: 'Saint Vincent and the Grenadines',
    WS: 'Samoa',
    SM: 'San Marino',
    ST: 'Sao Tome and Principe',
    SA: 'Saudi Arabia',
    SN: 'Senegal',
    RS: 'Serbia',
    SC: 'Seychelles',
    SL: 'Sierra Leone',
    SG: 'Singapore',
    SX: 'Sint Maarten (Dutch part)',
    SK: 'Slovakia',
    SI: 'Slovenia',
    SB: 'Solomon Islands',
    SO: 'Somalia',
    ZA: 'South Africa',
    GS: 'South Georgia and the South Sandwich Islands',
    SS: 'South Sudan',
    ES: 'Spain',
    LK: 'Sri Lanka',
    SD: 'Sudan',
    SR: 'Suriname',
    SZ: 'Swaziland',
    SE: 'Sweden',
    CH: 'Switzerland',
    SY: 'Syrian Arab Republic',
    TJ: 'Tajikistan',
    TZ: 'Tanzania, United Republic of',
    TH: 'Thailand',
    TL: 'Timor-Leste',
    TG: 'Togo',
    TK: 'Tokelau',
    TO: 'Tonga',
    TT: 'Trinidad and Tobago',
    TN: 'Tunisia',
    TR: 'Turkey',
    TM: 'Turkmenistan',
    TC: 'Turks and Caicos Islands',
    TV: 'Tuvalu',
    UG: 'Uganda',
    UA: 'Ukraine',
    AE: 'United Arab Emirates',
    GB: 'United Kingdom',
    US: 'United States',
    UM: 'United States Minor Outlying Islands',
    UY: 'Uruguay',
    UZ: 'Uzbekistan',
    VU: 'Vanuatu',
    VE: 'Venezuela, Bolivarian Republic of',
    VN: 'Viet Nam',
    VG: 'Virgin Islands, British',
    VI: 'Virgin Islands, U.S.',
    WF: 'Wallis and Futuna',
    EH: 'Western Sahara',
    YE: 'Yemen',
    ZM: 'Zambia',
    ZW: 'Zimbabwe',
  };

  var countries_zh = {
    阿富汗: '阿富汗',
    阿尔巴尼亚: '阿尔巴尼亚',
    阿尔及利亚: '阿尔及利亚',
    阿联酋: '阿联酋',
    阿鲁巴: '阿鲁巴',
    阿曼: '阿曼',
    阿塞拜疆: '阿塞拜疆',
    埃及: '埃及',
    埃塞俄比亚: '埃塞俄比亚',
    爱尔兰: '爱尔兰',
    爱沙尼亚: '爱沙尼亚',
    安道尔: '安道尔',
    安哥拉: '安哥拉',
    安圭拉: '安圭拉',
    安提瓜和巴布达: '安提瓜和巴布达',
    奥地利: '奥地利',
    澳大利亚: '澳大利亚',
    澳门: '澳门',
    巴巴多斯: '巴巴多斯',
    巴布亚新几内亚: '巴布亚新几内亚',
    巴哈马: '巴哈马',
    巴基斯坦: '巴基斯坦',
    巴拉圭: '巴拉圭',
    巴勒斯坦: '巴勒斯坦',
    巴林: '巴林',
    巴拿马: '巴拿马',
    巴西: '巴西',
    白俄罗斯: '白俄罗斯',
    百慕大: '百慕大',
    保加利亚: '保加利亚',
    北马里亚纳群岛: '北马里亚纳群岛',
    北马其顿: '北马其顿',
    贝宁: '贝宁',
    比利时: '比利时',
    冰岛: '冰岛',
    波多黎各: '波多黎各',
    波兰: '波兰',
    玻利维亚: '玻利维亚',
    波斯尼亚和黑塞哥维那: '波斯尼亚和黑塞哥维那',
    博茨瓦纳: '博茨瓦纳',
    伯利兹: '伯利兹',
    不丹: '不丹',
    布基纳法索: '布基纳法索',
    布隆迪: '布隆迪',
    布韦岛: '布韦岛',
    朝鲜: '朝鲜',
    赤道几内亚: '赤道几内亚',
    丹麦: '丹麦',
    德国: '德国',
    东帝汶: '东帝汶',
    多哥: '多哥',
    多米尼加: '多米尼加',
    多米尼加共和国: '多米尼加共和国',
    俄罗斯: '俄罗斯',
    厄瓜多尔: '厄瓜多尔',
    厄立特里亚: '厄立特里亚',
    法国: '法国',
    法罗群岛: '法罗群岛',
    法属波利尼西亚: '法属波利尼西亚',
    法属圭亚那: '法属圭亚那',
    法属圣马丁: '法属圣马丁',
    法属南部领地: '法属南部领地',
    梵蒂冈: '梵蒂冈',
    菲律宾: '菲律宾',
    斐济: '斐济',
    芬兰: '芬兰',
    佛得角: '佛得角',
    福克兰群岛: '福克兰群岛',
    冈比亚: '冈比亚',
    刚果: '刚果',
    刚果民主共和国: '刚果民主共和国',
    哥伦比亚: '哥伦比亚',
    哥斯达黎加: '哥斯达黎加',
    格林纳达: '格林纳达',
    格陵兰: '格陵兰',
    格鲁吉亚: '格鲁吉亚',
    根西岛: '根西岛',
    古巴: '古巴',
    瓜德罗普: '瓜德罗普',
    关岛: '关岛',
    圭亚那: '圭亚那',
    哈萨克斯坦: '哈萨克斯坦',
    海地: '海地',
    韩国: '韩国',
    荷兰: '荷兰',
    荷属圣马丁: '荷属圣马丁',
    赫德岛和麦克唐纳群岛: '赫德岛和麦克唐纳群岛',
    黑山: '黑山',
    洪都拉斯: '洪都拉斯',
    基里巴斯: '基里巴斯',
    吉布提: '吉布提',
    吉尔吉斯斯坦: '吉尔吉斯斯坦',
    几内亚: '几内亚',
    几内亚比绍: '几内亚比绍',
    加拿大: '加拿大',
    加纳: '加纳',
    加蓬: '加蓬',
    柬埔寨: '柬埔寨',
    捷克: '捷克',
    津巴布韦: '津巴布韦',
    喀麦隆: '喀麦隆',
    卡塔尔: '卡塔尔',
    开曼群岛: '开曼群岛',
    科科斯群岛: '科科斯群岛',
    科摩罗: '科摩罗',
    科特迪瓦: '科特迪瓦',
    科威特: '科威特',
    克罗地亚: '克罗地亚',
    肯尼亚: '肯尼亚',
    库克群岛: '库克群岛',
    库拉索: '库拉索',
    拉脱维亚: '拉脱维亚',
    莱索托: '莱索托',
    老挝: '老挝',
    黎巴嫩: '黎巴嫩',
    立陶宛: '立陶宛',
    利比里亚: '利比里亚',
    利比亚: '利比亚',
    列支敦士登: '列支敦士登',
    留尼汪: '留尼汪',
    卢森堡: '卢森堡',
    卢旺达: '卢旺达',
    罗马尼亚: '罗马尼亚',
    马达加斯加: '马达加斯加',
    马尔代夫: '马尔代夫',
    马耳他: '马耳他',
    马拉维: '马拉维',
    马来西亚: '马来西亚',
    马里: '马里',
    马绍尔群岛: '马绍尔群岛',
    马提尼克: '马提尼克',
    马约特: '马约特',
    毛里求斯: '毛里求斯',
    毛里塔尼亚: '毛里塔尼亚',
    美国: '美国',
    美国本土外小群岛: '美国本土外小群岛',
    美属萨摩亚: '美属萨摩亚',
    美属维尔京群岛: '美属维尔京群岛',
    蒙古: '蒙古',
    蒙特塞拉特: '蒙特塞拉特',
    孟加拉国: '孟加拉国',
    秘鲁: '秘鲁',
    密克罗尼西亚: '密克罗尼西亚',
    缅甸: '缅甸',
    摩尔多瓦: '摩尔多瓦',
    摩洛哥: '摩洛哥',
    摩纳哥: '摩纳哥',
    莫桑比克: '莫桑比克',
    墨西哥: '墨西哥',
    纳米比亚: '纳米比亚',
    南非: '南非',
    南乔治亚和南桑威奇群岛: '南乔治亚和南桑威奇群岛',
    南苏丹: '南苏丹',
    瑙鲁: '瑙鲁',
    尼泊尔: '尼泊尔',
    尼加拉瓜: '尼加拉瓜',
    尼日尔: '尼日尔',
    尼日利亚: '尼日利亚',
    纽埃: '纽埃',
    挪威: '挪威',
    诺福克岛: '诺福克岛',
    帕劳: '帕劳',
    皮特凯恩群岛: '皮特凯恩群岛',
    葡萄牙: '葡萄牙',
    日本: '日本',
    瑞典: '瑞典',
    瑞士: '瑞士',
    萨尔瓦多: '萨尔瓦多',
    萨摩亚: '萨摩亚',
    塞尔维亚: '塞尔维亚',
    塞拉利昂: '塞拉利昂',
    塞内加尔: '塞内加尔',
    塞浦路斯: '塞浦路斯',
    塞舌尔: '塞舌尔',
    沙特阿拉伯: '沙特阿拉伯',
    圣巴泰勒米: '圣巴泰勒米',
    圣多美和普林西比: '圣多美和普林西比',
    圣赫勒拿: '圣赫勒拿',
    圣基茨和尼维斯: '圣基茨和尼维斯',
    圣卢西亚: '圣卢西亚',
    圣马力诺: '圣马力诺',
    圣皮埃尔和密克隆群岛: '圣皮埃尔和密克隆群岛',
    圣文森特和格林纳丁斯: '圣文森特和格林纳丁斯',
    斯里兰卡: '斯里兰卡',
    斯洛伐克: '斯洛伐克',
    斯洛文尼亚: '斯洛文尼亚',
    斯威士兰: '斯威士兰',
    苏丹: '苏丹',
    苏里南: '苏里南',
    所罗门群岛: '所罗门群岛',
    索马里: '索马里',
    塔吉克斯坦: '塔吉克斯坦',
    泰国: '泰国',
    坦桑尼亚: '坦桑尼亚',
    汤加: '汤加',
    特克斯和凯科斯群岛: '特克斯和凯科斯群岛',
    特立尼达和多巴哥: '特立尼达和多巴哥',
    突尼斯: '突尼斯',
    图瓦卢: '图瓦卢',
    土耳其: '土耳其',
    土库曼斯坦: '土库曼斯坦',
    托克劳: '托克劳',
    瓦利斯和富图纳: '瓦利斯和富图纳',
    瓦努阿图: '瓦努阿图',
    危地马拉: '危地马拉',
    委内瑞拉: '委内瑞拉',
    文莱: '文莱',
    乌干达: '乌干达',
    乌克兰: '乌克兰',
    乌拉圭: '乌拉圭',
    乌兹别克斯坦: '乌兹别克斯坦',
    西班牙: '西班牙',
    希腊: '希腊',
    香港: '香港',
    新加坡: '新加坡',
    新喀里多尼亚: '新喀里多尼亚',
    新西兰: '新西兰',
    匈牙利: '匈牙利',
    叙利亚: '叙利亚',
    牙买加: '牙买加',
    亚美尼亚: '亚美尼亚',
    也门: '也门',
    伊拉克: '伊拉克',
    伊朗: '伊朗',
    以色列: '以色列',
    意大利: '意大利',
    印度: '印度',
    印度尼西亚: '印度尼西亚',
    英国: '英国',
    英属维尔京群岛: '英属维尔京群岛',
    英属印度洋领地: '英属印度洋领地',
    约旦: '约旦',
    越南: '越南',
    赞比亚: '赞比亚',
    乍得: '乍得',
    直布罗陀: '直布罗陀',
    智利: '智利',
    中非共和国: '中非共和国',
    中国: '中国',
  };

  if (typeof window !== 'undefined') {
    window.countries = countries;
    window.countries_zh = countries_zh;
  }

  return countries;
});
