document.addEventListener('DOMContentLoaded', function() {
    const payButton = document.getElementById('pay-button');
    const closeButton = document.querySelector('.close-btn');

    payButton.addEventListener('click', function() {
        // Change button state to "paying"
        payButton.textContent = '正在支付...';
        payButton.disabled = true;
        payButton.style.backgroundColor = '#06ad56'; // Darken the button color

        // Simulate a payment process
        setTimeout(function() {
            // On successful payment
            payButton.textContent = '支付成功';
            payButton.style.backgroundColor = '#07c160'; // Restore original color
            
            // You might want to redirect or show a success message here
            alert('支付成功！');
            
            // Re-enable the button after a while
            setTimeout(function() {
                payButton.textContent = '立即支付';
                payButton.disabled = false;
            }, 2000);

        }, 1500);
    });

    closeButton.addEventListener('click', function() {
        // This is a mock close button. In a real app, this would
        // trigger a native action to close the payment screen.
        if (confirm('您要取消支付吗？')) {
            alert('支付已取消。');
            // Here you might redirect the user or close the window/view
            // For example: window.close(); but this might not work in all browsers
        }
    });
}); 