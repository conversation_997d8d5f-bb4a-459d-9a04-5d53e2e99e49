{"name": "vue3-vite", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"autofit.js": "^3.2.8", "vue": "^3.5.13", "vue3-seamless-scroll": "^3.0.2"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/preset-env": "^7.26.9", "@rollup/plugin-babel": "^6.0.4", "@vitejs/plugin-legacy": "^6.1.0", "@vitejs/plugin-vue": "^5.2.2", "core-js": "^3.41.0", "vite": "^6.3.1"}}