// pages/index/index.js
Page({
  data: {
    detecting: false,          // 是否正在检测
    hideCamera: false,        // 是否隐藏相机
    blinkCount: 0,           // 眨眼次数
    blinkTimes: [],          // 记录每次眨眼的时间戳
    statusText: '点击开始检测',  // 状态文本
    buttonText: '开始检测',    // 按钮文本
    errorTips: '',           // 错误提示
    showInterval: false,     // 是否显示间隔时间
    maxInterval: 0,          // 最大间隔时间
    currentTimer: 0,         // 当前计时
    showTimer: false,        // 是否显示计时器
    frameAnalyzer: null,     // 帧分析器
    lastEyeState: 'open',    // 上一次眼睛状态
    timerInterval: null      // 计时器
  },

  onLoad() {
    // 检查相机权限
    this.checkCameraPermission()
  },

  onUnload() {
    this.stopDetection()
  },

  // 检查相机权限
  checkCameraPermission() {
    wx.authorize({
      scope: 'scope.camera',
      success: () => {
        console.log('相机权限已获取')
      },
      fail: () => {
        wx.showModal({
          title: '提示',
          content: '请允许使用摄像头权限，否则无法使用眨眼检测功能',
          showCancel: false,
          success: () => {
            wx.openSetting()
          }
        })
      }
    })
  },

  // 开始检测
  startDetection() {
    this.setData({
      detecting: true,
      blinkCount: 0,
      blinkTimes: [],
      errorTips: '',
      showInterval: false,
      maxInterval: 0,
      currentTimer: 0,
      showTimer: true,
      statusText: '请自然眨眼',
      lastEyeState: 'open'
    })

    // 创建帧分析器
    const frameAnalyzer = wx.createFrameAnalyzer()
    this.setData({ frameAnalyzer })

    // 启动计时器
    this.startTimer()

    // 开始分析视频帧
    this.analyzeFrame()
  },

  // 停止检测
  stopDetection() {
    if (this.data.frameAnalyzer) {
      this.data.frameAnalyzer.stop()
      this.data.frameAnalyzer = null
    }
    if (this.data.timerInterval) {
      clearInterval(this.data.timerInterval)
      this.data.timerInterval = null
    }
    this.setData({
      detecting: false,
      showTimer: false,
      buttonText: '重新测试'
    })
  },

  // 启动计时器
  startTimer() {
    let seconds = 0
    this.data.timerInterval = setInterval(() => {
      seconds++
      this.setData({
        currentTimer: seconds
      })
      
      // 如果超过30秒还没有检测到眨眼，停止检测
      if (seconds >= 30 && this.data.blinkCount < 1) {
        this.stopDetection()
        this.setData({
          errorTips: '未检测到眨眼，请重新测试！',
          statusText: '检测超时'
        })
      }
    }, 1000)
  },

  // 分析视频帧
  analyzeFrame() {
    const camera = wx.createCameraContext()
    
    const listener = camera.onCameraFrame((frame) => {
      if (!this.data.detecting) {
        listener.stop()
        return
      }

      // 这里使用帧分析器检测眨眼
      // 注意：实际的眨眼检测需要使用更复杂的计算机视觉算法
      // 这里使用简化的模拟逻辑
      this.detectBlink(frame)
    })

    listener.start()
  },

  // 检测眨眼（模拟实现）
  detectBlink(frame) {
    // 注意：这里使用随机模拟眨眼检测
    // 实际应用中需要使用计算机视觉算法进行真实的眨眼检测
    if (Math.random() < 0.05 && this.data.lastEyeState === 'open') {
      this.handleBlink()
    }
  },

  // 处理眨眼事件
  handleBlink() {
    const currentTime = Date.now()
    const blinkTimes = [...this.data.blinkTimes, currentTime]
    const blinkCount = this.data.blinkCount + 1

    this.setData({
      blinkCount,
      blinkTimes,
      lastEyeState: 'closed'
    })

    // 延迟将眼睛状态恢复为打开
    setTimeout(() => {
      this.setData({ lastEyeState: 'open' })
    }, 200)

    // 检查是否完成三次眨眼
    if (blinkCount >= 3) {
      this.calculateIntervals()
    }
  },

  // 计算眨眼间隔
  calculateIntervals() {
    const times = this.data.blinkTimes
    const interval1 = (times[1] - times[0]) / 1000  // 第1-2次间隔
    const interval2 = (times[2] - times[1]) / 1000  // 第2-3次间隔
    const maxInterval = Math.max(interval1, interval2)

    this.stopDetection()

    if (maxInterval < 1.5) {
      this.setData({
        errorTips: '本次测试眨眼间隔时间小于1.5秒，判定为无效检测，请重新测试！',
        statusText: '检测无效'
      })
    } else {
      this.setData({
        showInterval: true,
        maxInterval: maxInterval.toFixed(1),
        statusText: '检测完成'
      })
    }
  },

  // 处理相机错误
  handleError(e) {
    console.error('相机错误：', e.detail)
    this.stopDetection()
    this.setData({
      errorTips: '相机出现错误，请检查相机权限或重试',
      statusText: '检测失败'
    })
  }
})