/* General Styles */
body {
  font-family: 'Arial', sans-serif;
  line-height: 1.6;
  color: #333;
}

.container {
  max-width: 1200px;
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.bg-white {
  background-color: #fff;
}

.bg-cover {
  background-size: cover;
}

.shadow-md {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.border {
  border: 1px solid #ddd;
}

.rounded {
  border-radius: 8px;
}

.p-4 {
  padding: 16px;
}

.mt-4 {
  margin-top: 16px;
}

.mb-4 {
  margin-bottom: 16px;
}

.space-x-4 > :not(:first-child) {
  margin-left: 16px;
}

.space-y-4 > :not(:first-child) {
  margin-top: 16px;
}

.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-cols-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-cols-4 {
  grid-template-columns: repeat(4, 1fr);
}

.carousel {
  position: relative;
}

.carousel-inner {
  display: flex;
  transition: transform 0.5s ease;
}

.carousel-item {
  flex-shrink: 0;
}

.carousel-prev,
.carousel-next {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: white;
  border-radius: 50%;
  cursor: pointer;
}

.carousel-prev {
  left: 16px;
}

.carousel-next {
  right: 16px;
}

/* Specific Component Styles */
.header {
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-section {
  background-image: url('https://placehold.co/1920x1080 ');
  background-size: cover;
  background-position: center;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-section {
  background-image: url('https://placehold.co/1920x1080 ');
  background-size: cover;
  background-position: center;
  padding: 4rem 0;
}

.destinations-section {
  padding: 4rem 0;
}

.team-section {
  padding: 4rem 0;
}

.testimonials-section {
  padding: 4rem 0;
}

.footer {
  padding: 4rem 0;
}

/* Icons */
.fab, .fas {
  font-size: 1.2rem;
}