<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>GrapesJS Custom Asset Manager</title>
  <link href="https://unpkg.com/grapesjs/dist/css/grapes.min.css" rel="stylesheet">
  <script src="https://unpkg.com/grapesjs"></script>
  <style>
    /* 自定义资源管理器模态框样式 */
    #custom-asset-modal {
      display: none;
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: white;
      padding: 20px;
      border: 1px solid #ddd;
      z-index: 1000;
      width: 80%;
      max-width: 600px;
    }
    .asset-item {
      display: inline-block;
      margin: 10px;
      text-align: center;
      cursor: pointer;
    }
    .asset-item img {
      width: 100px;
      height: 100px;
      object-fit: cover;
    }
    #asset-upload {
      margin: 20px 0;
    }
  </style>
</head>
<body>

<div id="gjs"></div>
<!-- 自定义资源管理器 UI -->
<button id="open-asset-manager">Open Asset Manager</button>
<div id="custom-asset-modal">
  <h3>Custom Asset Manager</h3>
  <input type="file" id="asset-upload" multiple accept="image/*">
  <div id="assets-container"></div>
  <button id="close-asset-modal">Close</button>
</div>

<script>
  const editor = grapesjs.init({
    container: '#gjs',
    height: '100vh',
    fromElement: true,
    storageManager: false,
    // 启用自定义资源管理器
    assetManager: {
      custom: true,  // 关键设置
      assets: [
        // 初始资源（可选）
        { src: 'https://via.placeholder.com/150', name: 'Placeholder 1' }
      ]
    },
    blockManager: {
      blocks: [
        {
          id: 'image',
          label: 'Image',
          content: {
            type: 'image',
            attributes: { alt: 'Image' }
          }
        }
      ]
    }
  });

  // 获取 Asset Manager 实例
  const assetManager = editor.AssetManager;

  // DOM 元素
  const openBtn = document.getElementById('open-asset-manager');
  const modal = document.getElementById('custom-asset-modal');
  const uploadInput = document.getElementById('asset-upload');
  const assetsContainer = document.getElementById('assets-container');
  const closeBtn = document.getElementById('close-asset-modal');

  // 打开资源管理器
  openBtn.addEventListener('click', () => {
    modal.style.display = 'block';
    renderAssets();
  });

  // 关闭资源管理器
  closeBtn.addEventListener('click', () => {
    modal.style.display = 'none';
  });

  // 渲染资源列表
  function renderAssets() {
    assetsContainer.innerHTML = '';
    const assets = assetManager.getAll();
    
    assets.forEach(asset => {
      const assetEl = document.createElement('div');
      assetEl.className = 'asset-item';
      
      // 显示图片缩略图
      if (asset.get('type') === 'image') {
        const img = document.createElement('img');
        img.src = asset.get('src');
        assetEl.appendChild(img);
      }
      
      // 显示资源名称
      const name = document.createElement('div');
      name.textContent = asset.get('name') || 'Unnamed';
      assetEl.appendChild(name);
      
      // 点击选择资源
      assetEl.addEventListener('click', () => {
        // 触发资源选择（更新选中的组件）
        assetManager.select(asset);
        modal.style.display = 'none';
      });
      
      assetsContainer.appendChild(assetEl);
    });
  }

  // 处理文件上传
  uploadInput.addEventListener('change', (e) => {
    const files = e.target.files;
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const reader = new FileReader();
      
      reader.onload = (event) => {
        // 添加新资源（使用 Blob URL）
        assetManager.add({
          type: 'image',
          src: event.target.result,
          name: file.name,
          file // 保留文件引用（可选）
        });
        renderAssets(); // 刷新列表
      };
      
      reader.readAsDataURL(file);
    }
  });

  // 监听资源添加事件（可选）
//   assetManager.on('add', () => {
//     console.log('Asset added:', assetManager.getAll());
//   });

//   // 监听资源选择事件（可选）
//   assetManager.on('asset:select', (asset) => {
//     console.log('Asset selected:', asset.get('src'));
//   });
</script>
</body>
</html>
