<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信支付 - WeixinJSBridge实现</title>
    <script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
        }
        
        body {
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        header {
            background: linear-gradient(135deg, #07c160, #06ad56);
            color: white;
            padding: 30px 20px;
            border-radius: 12px 12px 0 0;
            text-align: center;
            margin-bottom: 30px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        
        header h1 {
            margin-bottom: 15px;
            font-size: 28px;
            font-weight: 500;
        }
        
        header p {
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
        }
        
        .card {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            position: relative;
            overflow: hidden;
        }
        
        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 5px;
            height: 100%;
            background: #07c160;
        }
        
        .card h2 {
            margin-bottom: 20px;
            color: #07c160;
            font-weight: 500;
        }
        
        .order-info {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px dashed #eaeaea;
        }
        
        .order-info > div {
            flex: 1;
            min-width: 180px;
            padding: 8px 0;
        }
        
        .product-card {
            display: flex;
            padding: 15px;
            background: #fafafa;
            border-radius: 8px;
            margin-bottom: 20px;
            align-items: center;
        }
        
        .product-image {
            width: 80px;
            height: 80px;
            border-radius: 8px;
            background: linear-gradient(135deg, #07c160, #06ad56);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 20px;
            color: white;
            font-size: 32px;
        }
        
        .product-details {
            flex: 1;
        }
        
        .product-title {
            font-size: 18px;
            margin-bottom: 6px;
        }
        
        .product-price {
            color: #07c160;
            font-size: 22px;
            font-weight: 500;
            padding-top: 5px;
        }
        
        .product-price::before {
            content: "¥";
            font-size: 0.8em;
            margin-right: 2px;
        }
        
        .btn-container {
            display: flex;
            justify-content: center;
            margin: 25px 0;
            gap: 20px;
        }
        
        .btn {
            padding: 15px 35px;
            border: none;
            border-radius: 30px;
            font-size: 18px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s;
            outline: none;
            text-align: center;
            min-width: 200px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #07c160, #06ad56);
            color: white;
            box-shadow: 0 4px 12px rgba(7, 193, 96, 0.3);
        }
        
        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 15px rgba(7, 193, 96, 0.4);
        }
        
        .btn-outline {
            background: transparent;
            border: 2px solid #07c160;
            color: #07c160;
        }
        
        .btn-outline:hover {
            background-color: rgba(7, 193, 96, 0.05);
        }
        
        .steps {
            display: flex;
            justify-content: space-between;
            padding: 15px 0;
            margin: 25px 0;
            counter-reset: step;
        }
        
        .step {
            flex: 1;
            text-align: center;
            position: relative;
            padding: 0 20px;
        }
        
        .step:not(:last-child)::after {
            content: '';
            position: absolute;
            top: 30px;
            right: -15px;
            width: 60px;
            height: 2px;
            background-color: #07c160;
        }
        
        .step::before {
            counter-increment: step;
            content: counter(step);
            width: 60px;
            height: 60px;
            line-height: 60px;
            border-radius: 50%;
            background: #07c160;
            color: white;
            font-size: 24px;
            font-weight: 500;
            display: block;
            margin: 0 auto 15px;
        }
        
        .step-title {
            font-weight: 500;
            margin-bottom: 8px;
        }
        
        .tip-container {
            background: #e9f7f0;
            padding: 20px;
            border-radius: 8px;
            margin: 25px 0;
        }
        
        .tip-title {
            color: #07c160;
            font-weight: 500;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        
        .tip-title::before {
            content: "!";
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            background-color: #07c160;
            color: white;
            border-radius: 50%;
            margin-right: 10px;
            font-weight: bold;
        }
        
        .tip-content li {
            margin-bottom: 8px;
            position: relative;
            padding-left: 20px;
        }
        
        .tip-content li::before {
            content: "•";
            position: absolute;
            left: 0;
            color: #07c160;
        }
        
        .status {
            padding: 10px 15px;
            border-radius: 30px;
            margin: 20px 0;
            text-align: center;
            font-weight: 500;
            background: #f8f9fa;
            opacity: 0;
            transform: translateY(10px);
            transition: all 0.5s ease;
        }
        
        .status.show {
            opacity: 1;
            transform: translateY(0);
        }
        
        .status.success {
            background: #e9f7f0;
            color: #07c160;
        }
        
        .status.error {
            background: #fdefef;
            color: #e74c3c;
        }
        
        footer {
            text-align: center;
            padding: 20px;
            color: #666;
            font-size: 14px;
            margin-top: 30px;
        }
        
        @media (max-width: 600px) {
            .steps {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .step {
                width: 100%;
                padding: 15px 0;
                text-align: left;
                padding-left: 80px;
            }
            
            .step::before {
                position: absolute;
                left: 0;
            }
            
            .step:not(:last-child)::after {
                display: none;
            }
            
            .btn-container {
                flex-direction: column;
                gap: 15px;
            }
            
            .btn {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>微信JSAPI支付演示</h1>
            <p>使用WeixinJSBridge.invoke实现安全可靠的支付体验</p>
        </header>
        
        <section class="card">
            <h2>商品信息</h2>
            <div class="product-card">
                <div class="product-image">📱</div>
                <div class="product-details">
                    <h3 class="product-title">微信小程序高级开发课程</h3>
                    <p>包含小程序开发技巧、高级API使用、支付集成等核心内容</p>
                </div>
                <div class="product-price">299</div>
            </div>
        </section>
        
        <section class="card">
            <h2>订单信息</h2>
            <div class="order-info">
                <div>
                    <strong>订单编号：</strong> WXPAY202308150001
                </div>
                <div>
                    <strong>订单创建时间：</strong> 2023-08-15 14:25:36
                </div>
                <div>
                    <strong>支付方式：</strong> 微信支付
                </div>
                <div>
                    <strong>支付期限：</strong> 30分钟内支付
                </div>
            </div>
        </section>
        
        <section class="card">
            <h2>支付流程</h2>
            
            <div class="steps">
                <div class="step">
                    <div class="step-title">准备支付参数</div>
                    <div class="step-desc">后端生成支付所需参数</div>
                </div>
                <div class="step">
                    <div class="step-title">检测WeixinJSBridge</div>
                    <div class="step-desc">验证是否在微信环境中</div>
                </div>
                <div class="step">
                    <div class="step-title">调用支付</div>
                    <div class="step-desc">发起支付请求</div>
                </div>
                <div class="step">
                    <div class="step-title">处理结果</div>
                    <div class="step-desc">处理支付结果</div>
                </div>
            </div>
            
            <div id="statusBox" class="status">等待操作...</div>
            
            <div class="btn-container">
                <button id="payBtn" class="btn btn-primary">立即支付 ¥299</button>
                <button id="testBtn" class="btn btn-outline">测试支付功能</button>
            </div>
            
            <div class="tip-container">
                <div class="tip-title">重要提示</div>
                <div class="tip-content">
                    <ul>
                        <li>请确保在微信内置浏览器中访问此页面</li>
                        <li>支付参数必须通过后端生成（前端示例仅为演示）</li>
                        <li>实际签名参数使用RSA方式，签名在服务端完成</li>
                        <li>最终支付结果请以后端异步通知为准</li>
                        <li>测试支付不会发生实际扣款</li>
                    </ul>
                </div>
            </div>
        </section>
        
        <footer>
            <p>© 2023 微信支付演示页面 - 仅供技术参考学习</p>
        </footer>
    </div>

    <script>
        const payBtn = document.getElementById('payBtn');
        const testBtn = document.getElementById('testBtn');
        const statusBox = document.getElementById('statusBox');
        
        // 更新状态显示
        function updateStatus(message, isSuccess) {
            statusBox.textContent = message;
            statusBox.className = `status ${isSuccess ? 'success' : 'error'} show`;
        }
        
        // 检测WeixinJSBridge是否可用
        function checkWeixinJSBridge() {
            if (typeof WeixinJSBridge === 'undefined') {
                updateStatus('错误：WeixinJSBridge未定义，请在微信内置浏览器中打开', false);
                return false;
            }
            return true;
        }
        
        // 调起微信支付（实际应由后端生成参数）
        function invokeWeixinPay(isTest = false) {
            if (!checkWeixinJSBridge()) return;
            
            // 显示支付状态
            updateStatus('正在请求支付接口...', true);
            
            // 在实际项目中，这些参数应由后端API提供
            // 此处仅为演示，请勿在生产环境使用
            const payParams = {
                appId: 'wx1234567890abcdef',       // 公众号ID
                timeStamp: Math.floor(Date.now() / 1000).toString(), // 时间戳（秒级）
                nonceStr: 'n_' + Math.random().toString(36).substr(2, 15), // 随机字符串
                package: 'prepay_id=wx1516171819abcdefghijklmnopqrstu',  // 预支付交易会话ID
                signType: 'RSA',                   // 签名方式
                paySign: 'a1b2c3d4e5f6g7h8i9j0....' // 签名内容（实际由服务端生成）
            };
            
            if (isTest) {
                payParams.package = 'TEST_package_' + Math.random().toString(36).substr(2, 10);
            }
            
            // 模拟请求延时
            setTimeout(() => {
                // 调用WeixinJSBridge支付接口
                WeixinJSBridge.invoke(
                    'getBrandWCPayRequest',
                    payParams,
                    function(res) {
                        // 处理支付结果
                        updateStatus('支付处理完成', true);
                        
                        // 判断支付结果
                        if (res.err_msg === 'get_brand_wcpay_request:ok') {
                            setTimeout(() => {
                                updateStatus('支付成功！感谢您的购买！', true);
                            }, 500);
                        } else if (res.err_msg === 'get_brand_wcpay_request:cancel') {
                            updateStatus('支付已取消，如有需要请重新支付', false);
                        } else {
                            updateStatus(`支付失败: ${res.err_msg || '未知错误'}`, false);
                        }
                    }
                );
            }, 800);
        }
        
        // 支付按钮事件
        payBtn.addEventListener('click', function() {
            updateStatus('正在准备支付参数...', true);
            setTimeout(() => invokeWeixinPay(), 1000);
        });
        
        // 测试按钮事件
        testBtn.addEventListener('click', function() {
            updateStatus('正在启动支付测试环境...', true);
            setTimeout(() => invokeWeixinPay(true), 1000);
        });
        
        // 初始化检测
        window.onload = function() {
            if (typeof WeixinJSBridge === 'undefined') {
                updateStatus('提示：当前环境不支持支付功能，请在微信内打开', false);
            } else {
                updateStatus('支付功能已准备就绪', true);
            }
        };
    </script>
</body>
</html>