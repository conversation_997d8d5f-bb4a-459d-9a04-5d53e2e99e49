import { createApp } from 'vue';
import './style.css';
import App from './App.vue';

document.addEventListener('DOMContentLoaded', async () => {
  const app = createApp(App);
  app.mount('#app');
});


// 函数防抖
function debounce(fn, delay) {
  let timer = null;
  return function () {
    clearTimeout(timer);
    timer = setTimeout(() => fn.apply(this, arguments), delay);
  };
}

// 函数节流
function throttle(fn, delay) {
  let timer = null;
  return function () {
    if (timer) return;
    timer = setTimeout(() => {
      fn.apply(this, arguments);
      timer = null;
    }, delay);
  };
}
