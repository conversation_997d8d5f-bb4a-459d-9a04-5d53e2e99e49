import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import legacy from '@vitejs/plugin-legacy';

export default defineConfig({
  base: './',
  plugins: [
    vue(), // Vue 插件
    legacy({
      targets: ['ie >= 9'], // 需要兼容的目标列表，可以设置多个
      additionalLegacyPolyfills: ['regenerator-runtime/runtime'], // 面向IE11时需要此插件

      renderLegacyChunks: true,
      polyfills: true,
      modernPolyfills: true,
    }),
  ],
  build: {
    rollupOptions: {
      output: {
        manualChunks(id) {
          if (id.includes('node_modules')) {
            return 'vendor'; // 将第三方依赖打包到单独的 chunk 中
          }
        },
      },
    },
  },
  server: {
    port: 8822,
  },
});
