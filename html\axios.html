<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>仓储综合分析看板</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
      /* 简单设置看板整体样式 */
      .dashboard {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        grid-gap: 20px;
        background-color: #1c1f23;
        color: #ccc;
        padding: 20px;
      }
    </style>
  </head>

  <body>
    <div class="dashboard">
      <!-- 线边物料接收的统计柱状图容器 -->
      <div id="inboundBarChart" style="width: 300px; height: 200px"></div>
      <!-- 线边物料接收的统计柱状图容器 -->
      <div id="outboundBarChart" style="width: 300px; height: 200px"></div>
      <!-- 本年度库存分析环形图容器 -->
      <div id="inventoryRingChart" style="width: 300px; height: 200px"></div>
      <!-- 本月度作业统计仪表盘容器 -->
      <div id="operationDashboard" style="width: 300px; height: 200px"></div>
      <!-- 本月度告警动态柱状图容器 -->
      <div id="alarmBarChart" style="width: 300px; height: 200px"></div>
    </div>
    <script>
      // 后续 JavaScript 代码
      // 线边物料接收（入库）柱状图
      var inboundBarChart = echarts.init(document.getElementById('inboundBarChart'));
      var inboundBarOption = {
        xAxis: {
          type: 'category',
          data: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'],
          axisLine: {
            lineStyle: {
              color: '#ccc',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: '#ccc',
          },
        },
        yAxis: {
          type: 'value',
          axisLine: {
            lineStyle: {
              color: '#ccc',
            },
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: '#ccc',
          },
          splitLine: {
            lineStyle: {
              color: '#333',
            },
          },
        },
        series: [
          {
            data: [400, 420, 450, 400, 430, 460, 440, 470, 450, 480, 460, 490], // 模拟数据
            type: 'bar',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#00c6ff' },
                { offset: 1, color: '#007bff' },
              ]),
            },
          },
        ],
      };
      inboundBarChart.setOption(inboundBarOption);

      // 线边物料接收（出库）柱状图
      var outboundBarChart = echarts.init(document.getElementById('outboundBarChart'));
      var outboundBarOption = {
        // 与入库柱状图类似配置，此处省略重复部分
        xAxis: {
          type: 'category',
          data: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'],
          // 样式配置
        },
        yAxis: {
          type: 'value',
          // 样式配置
        },
        series: [
          {
            data: [350, 380, 360, 370, 390, 385, 395, 400, 390, 410, 400, 420], // 模拟数据
            type: 'bar',
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#00c6ff' },
                { offset: 1, color: '#007bff' },
              ]),
            },
          },
        ],
      };
      outboundBarChart.setOption(outboundBarOption);

      // 本年度库存分析环形图
      var inventoryRingChart = echarts.init(document.getElementById('inventoryRingChart'));
      var inventoryRingOption = {
        series: [
          {
            name: '库存分析',
            type: 'pie',
            radius: ['50%', '70%'], // 环形大小
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center',
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '30',
                fontWeight: 'bold',
              },
            },
            labelLine: {
              show: false,
            },
            data: [
              { value: 300, name: '物料1' },
              { value: 250, name: '物料2' },
              { value: 400, name: '物料3' },
              { value: 150, name: '物料4' },
              { value: 200, name: '物料5' },
            ],
          },
        ],
      };
      inventoryRingChart.setOption(inventoryRingOption);

      // 本月度作业统计仪表盘
      var operationDashboard = echarts.init(document.getElementById('operationDashboard'));
      var operationDashboardOption = {
        series: [
          {
            name: '作业次数',
            type: 'gauge',
            detail: {
              formatter: '{value} 次',
              fontSize: 20,
              color: '#00c6ff',
            },
            data: [{ value: 624, name: '作业总次数' }],
          },
        ],
      };
      operationDashboard.setOption(operationDashboardOption);

      // 本月度告警动态柱状图
      var alarmBarChart = echarts.init(document.getElementById('alarmBarChart'));
      var alarmBarOption = {
        xAxis: {
          type: 'category',
          data: ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12'],
          // 样式配置
        },
        yAxis: {
          type: 'value',
          // 样式配置
        },
        series: [
          {
            name: '库存告警',
            data: [20, 18, 22, 20, 19, 21, 23, 20, 22, 21, 23, 20], // 模拟数据
            type: 'bar',
            itemStyle: {
              color: 'red',
            },
          },
          {
            name: '库龄告警',
            data: [2, 3, 1, 2, 1, 3, 2, 1, 3, 2, 1, 3], // 模拟数据
            type: 'bar',
            itemStyle: {
              color: 'orange',
            },
          },
          {
            name: '温湿度告警',
            data: [1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], // 模拟数据
            type: 'bar',
            itemStyle: {
              color: 'green',
            },
          },
        ],
      };
      alarmBarChart.setOption(alarmBarOption);
    </script>
  </body>
</html>
