<template>
  <div class="vertical-scoll">
    <vue3-seamless-scroll :list="list" ref="scrollRef">
      <template v-slot="{ data }">
        <span style="width: 100%; display: block; line-height: 30px">
          <div>{{ data?.name }}</div>
        </span>
      </template>
    </vue3-seamless-scroll>
  </div>
</template>
<script>
  import { defineComponent, ref } from 'vue';
  import { Vue3SeamlessScroll, VerticalScroll, HorizontalScroll } from 'vue3-seamless-scroll';

  const listData = Array.from({ length: 10000 }, (_, i) => ({
    id: Date.now() + i + 1,
    name: `Vue3.0无缝滚动展示数据第${i + 1}条`,
  }));

  export default defineComponent({
    name: 'App',
    components: {
      Vue3SeamlessScroll,
      VerticalScroll,
      HorizontalScroll,
    },
    setup() {
      const list = ref([]);
      const scrollRef = ref(null);
      setTimeout(() => {
        list.value = listData;
        scrollRef.value.reset();
      }, 1000);
      return {
        list: list,
        scrollRef,
      };
    },
  });
</script>

<style>
  .vertical-scoll {
    overflow: hidden;
    height: 300px;
  }

  .horizonta-scoll {
    overflow: hidden;
    height: 300px;
  }

  .vertical-text {
    height: 300px;
    writing-mode: vertical-lr;
    text-orientation: upright;
    line-height: 30px;
    display: inline-block;
  }
</style>
