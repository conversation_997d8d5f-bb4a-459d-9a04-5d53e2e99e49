<script setup>
  import HelloWorld from './components/HelloWorld.vue';
  import { computed } from 'vue';
  const className = computed(() => {
    return 0 ? 'logo' : 'logo vue';
  });
</script>

<template>
  <div>
    <a href="https://vite.dev" target="_blank" :class="className">
      <img src="/vite.svg" alt="Vite logo" />
    </a>
  </div>
  <HelloWorld msg="Vite + Vue" />
</template>

<style scoped>
  .logo {
    height: 6em;
    padding: 1.5em;
    will-change: filter;
    transition: filter 300ms;
  }
  .logo.vue {
    height: 6em;
    padding: 1.5em;
    will-change: filter;
    transition: filter 300ms;
    background-color: #42b883;
  }
  .logo:hover {
    filter: drop-shadow(0 0 2em #646cffaa);
  }
  .logo.vue:hover {
    filter: drop-shadow(0 0 2em #42b883aa);
  }
</style>
