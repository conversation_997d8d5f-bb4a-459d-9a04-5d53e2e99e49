<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
</head>
<body>
    <script src="https://cdn.bootcdn.net/ajax/libs/js-beautify/1.15.3/beautifier.min.js"></script>
    <script>
        const {css, html, js} = beautifier;
        const beautify = html(`<div meeting_id="2" template_id="1" language="cn" id="i3ph" class="navbar navbar-meenow meenow-c2422"><div class="navbar-container meenow-c2427"><div class="navbar-logo meenow-c2431"><img src="https://dummyimage.com/100x60/45465e/fff" alt="Logo" target="_self" id="i4i14" class="meenow-c2435"/></div><div class="navbar meenow-c2454"><a href="#" target="_self" class="navbar-link meenow-c2457">导航一</a><a href="#" target="_self" class="navbar-link meenow-c2478">导航二</a><a href="#" target="_self" class="navbar-link meenow-c2498">导航三</a><a href="#" target="_self" class="navbar-link meenow-c2518">导航四</a></div><div class="navbar-right meenow-c2552"><a href="#" target="_self" class="navbar-link login-btn meenow-c2556">报名参会</a></div></div></div>`);

        console.log('beautify: ', beautify);

        const jsBeautify = js(`var items = document.querySelectorAll('#i3ph');
          for (var i = 0, len = items.length; i < len; i++) {
            (function(){
var that = this;
                    var meeting_id = this.getAttribute('meeting_id');
                    var template_id = this.getAttribute('template_id');
                    var language = this.getAttribute('language');
                    function createMenu(data, level) {
                        if (level === void 0) { level = 1; }
                        var ul = document.createElement('ul');
                        ul.className = "menu-level-".concat(level);
                        data.forEach(function (item) {
                            var li = document.createElement('li');
                            var a = document.createElement('a');
                            a.href = item.url;
                            a.textContent = language ? (language === 'cn' ? item.name : item.e_name) : item.name;
                            li.appendChild(a);
                            if (item.son) {
                                li.appendChild(createMenu(item.son, level + 1));
                            }
                            ul.appendChild(li);
                        });
                        return ul;
                    }
                    // 从后端获取导航数据
                    var url = '/api/index/VisualCategory?meeting_id=' + meeting_id + '&template_id=' + template_id;
                    fetch(url, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    })
                        .then(function (response) {
                        return response.json();
                    })
                        .then(function (data) {
                        if (data) {
                            var navContent = createMenu(data).outerHTML;
                            that.querySelector('.navbar').innerHTML = navContent;
                        }
                    })
                        .catch(function (error) { return console.error('Error loading menu:', error); });
}.bind(items[i]))();
          }
          var items = document.querySelectorAll('#i4i14');
          for (var i = 0, len = items.length; i < len; i++) {
            (function(){
var linkUrl = this.getAttribute('linkUrl');
                    var target = this.getAttribute('target');
                    var isEdit = window.parent.isEdit || false;
                    if (linkUrl && !isEdit) {
                        this.onclick = function () {
                            window.open(linkUrl, target);
                        };
                    }
}.bind(items[i]))();
          }
        `);

        console.log( jsBeautify);

        // const beautifier = new Beautifier();
        // console.log('beautifier: ', beautifier);//
    </script>
</body>
</html>