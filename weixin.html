<body>
  <header id="icl1" class="header-banner">
    <div id="ib9l" class="container-width">
      <div class="logo-container">
        <div class="logo">GrapesJS
        </div>
      </div>
      <nav class="menu">
        <div class="menu-item">BUILDER
        </div>
        <div class="menu-item">TEMPLATE
        </div>
        <div class="menu-item">WEB
        </div>
      </nav>
      <div class="clearfix">
      </div>
      <div class="lead-title">Build your templates without coding
      </div>
      <div id="i6wv3" class="sub-lead-title">All text blocks could be edited easily with double clicking on it. You can create new text blocks with the command from the left panel
      </div>
      <div id="izz58" class="lead-btn">Hover me
      </div>
    </div>
  </header>
  <section id="is92l" class="flex-sect">
    <div class="container-width">
      <div id="iklu3" class="flex-title">Flex is the new black
      </div>
      <div class="flex-desc">With flexbox system you're able to build complex layouts easily and with free responsivity
      </div>
      <div data-tooltip="Tooltip" id="i5uayi" class="tooltip-component tooltip-component--empty">
      </div>
      <div class="cards">
        <div class="card">
          <div id="iqn7s" class="card-header">
          </div>
          <div class="card-body">
            <div class="card-title">Title one
            </div>
            <div class="card-sub-title">Subtitle one
            </div>
            <div class="card-desc">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore
            </div>
          </div>
        </div>
        <div class="card">
          <div id="izzi6" class="card-header ch2">
          </div>
          <div class="card-body">
            <div class="card-title">Title two
            </div>
            <div class="card-sub-title">Subtitle two
            </div>
            <div class="card-desc">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore
            </div>
          </div>
        </div>
        <div class="card">
          <div class="card-header ch3">
          </div>
          <div class="card-body">
            <div class="card-title">Title three
            </div>
            <div class="card-sub-title">Subtitle three
            </div>
            <div class="card-desc">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore
            </div>
          </div>
        </div>
        <div class="card">
          <div class="card-header ch4">
          </div>
          <div class="card-body">
            <div class="card-title">Title four
            </div>
            <div class="card-sub-title">Subtitle four
            </div>
            <div class="card-desc">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore
            </div>
          </div>
        </div>
        <div class="card">
          <div class="card-header ch5">
          </div>
          <div class="card-body">
            <div class="card-title">Title five
            </div>
            <div class="card-sub-title">Subtitle five
            </div>
            <div class="card-desc">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore
            </div>
          </div>
        </div>
        <div class="card">
          <div class="card-header ch6">
          </div>
          <div class="card-body">
            <div class="card-title">Title six
            </div>
            <div class="card-sub-title">Subtitle six
            </div>
            <div class="card-desc">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  <section class="am-sect">
    <div class="container-width">
      <div class="am-container">
        <img src="/assets/images/demos/phone-app.png" id="ivafv" class="img-phone"/>
        <div class="am-content">
          <div class="am-pre">ASSET MANAGER
          </div>
          <div class="am-title">Manage your images with Asset Manager
          </div>
          <div class="am-desc">You can create image blocks with the command from the left panel and edit them with double click
          </div>
          <div class="am-post">Image uploading is not allowed in this demo
          </div>
        </div>
      </div>
    </div>
  </section>
  <section class="blk-sect">
    <div class="container-width">
      <div class="blk-title">Blocks
      </div>
      <div class="blk-desc">Each element in HTML page could be seen as a block. On the left panel you could find different kind of blocks that you can create, move and style
      </div>
      <div class="price-cards">
        <div class="price-card-cont">
          <div class="price-card">
            <div class="pc-title">Starter
            </div>
            <div class="pc-desc">Some random list
            </div>
            <div class="pc-feature odd-feat">+ Starter feature 1
            </div>
            <div class="pc-feature">+ Starter feature 2
            </div>
            <div class="pc-feature odd-feat">+ Starter feature 3
            </div>
            <div class="pc-feature">+ Starter feature 4
            </div>
            <div class="pc-amount odd-feat">$ 9,90/mo
            </div>
          </div>
        </div>
        <div class="price-card-cont">
          <div class="price-card pc-regular">
            <div class="pc-title">Regular
            </div>
            <div class="pc-desc">Some random list
            </div>
            <div class="pc-feature odd-feat">+ Regular feature 1
            </div>
            <div class="pc-feature">+ Regular feature 2
            </div>
            <div class="pc-feature odd-feat">+ Regular feature 3
            </div>
            <div class="pc-feature">+ Regular feature 4
            </div>
            <div id="ih0owy" class="pc-amount odd-feat">$ 19,90/mo
            </div>
          </div>
        </div>
        <div class="price-card-cont">
          <div class="price-card pc-enterprise">
            <div class="pc-title">Enterprise
            </div>
            <div class="pc-desc">Some random list
            </div>
            <div class="pc-feature odd-feat">+ Enterprise feature 1
            </div>
            <div class="pc-feature">+ Enterprise feature 2
            </div>
            <div class="pc-feature odd-feat">+ Enterprise feature 3
            </div>
            <div class="pc-feature">+ Enterprise feature 4
            </div>
            <div class="pc-amount odd-feat">$ 29,90/mo
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
  <section id="iagjxu" class="bdg-sect">
    <div id="iwrsax">
      <div role="tablist" class="tab-container">
        <div role="tab" aria-controls="i2qhde" id="i1mk5l" class="tab">
          <span>Tab</span>
        </div>
        <div role="tab" aria-controls="imu2el" id="iz7qkf" class="tab">
          <span>Tab</span>
        </div>
        <div role="tab" aria-controls="iv2drm" id="iczha2" class="tab">
          <span>Tab</span>
        </div>
      </div>
      <div class="tab-contents">
        <div role="tabpanel" id="i2qhde" aria-labelledby="i1mk5l" hidden class="tab-content">
          <div>Tab Content
          </div>
        </div>
        <div role="tabpanel" id="imu2el" aria-labelledby="iz7qkf" hidden class="tab-content">
          <div>Tab Content
          </div>
        </div>
        <div role="tabpanel" id="iv2drm" aria-labelledby="iczha2" hidden class="tab-content">
          <div>Tab Content
          </div>
        </div>
      </div>
    </div>
    <div class="container-width">
      <h1 class="bdg-title">The team
      </h1>
      <div class="badges">
        <div class="badge">
          <div class="badge-header">
          </div>
          <img src="/assets/images/demos/team1.jpg" id="ixawvz" class="badge-avatar"/>
          <div class="badge-body">
            <div class="badge-name">Adam Smith
            </div>
            <div class="badge-role">CEO
            </div>
            <div class="badge-desc">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore ipsum dolor sit
            </div>
          </div>
          <div class="badge-foot">
            <span class="badge-link">f</span>
            <span class="badge-link">t</span>
            <span class="badge-link">ln</span>
          </div>
        </div>
        <div class="badge">
          <div id="i7a39e" class="badge-header">
          </div>
          <img src="https://via.placeholder.com/350x250/f28c33/fff" id="ikv3mw" class="badge-avatar"/>
          <div class="badge-body">
            <div class="badge-name">John Black
            </div>
            <div class="badge-role">Software Engineer
            </div>
            <div id="ikno7h" class="badge-desc">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore ipsum dolor sit
            </div>
          </div>
          <div class="badge-foot">
            <span class="badge-link">f</span>
            <span id="i07uvb" class="badge-link">t</span>
            <span class="badge-link">ln</span>
          </div>
        </div>
        <div class="badge">
          <div class="badge-header">
          </div>
          <img src="/assets/images/demos/team3.jpg" class="badge-avatar"/>
          <div class="badge-body">
            <div class="badge-name">Jessica White
            </div>
            <div class="badge-role">Web Designer
            </div>
            <div class="badge-desc">Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore ipsum dolor sit
            </div>
          </div>
          <div class="badge-foot">
            <span class="badge-link">f</span>
            <span class="badge-link">t</span>
            <span class="badge-link">ln</span>
          </div>
        </div>
      </div>
    </div>
  </section>
  <footer class="footer-under">
    <div id="i0wct6" class="countdown">
      <span data-js="countdown" class="countdown-cont"><div class="countdown-block">
        <div data-js="countdown-day" class="countdown-digit">
        </div>
        <div class="countdown-label">days
        </div>
        </div><div class="countdown-block">
        <div data-js="countdown-hour" class="countdown-digit">
        </div>
        <div class="countdown-label">hours
        </div>
        </div><div class="countdown-block">
        <div data-js="countdown-minute" class="countdown-digit">
        </div>
        <div class="countdown-label">minutes
        </div>
        </div><div class="countdown-block">
        <div data-js="countdown-second" class="countdown-digit">
        </div>
        <div class="countdown-label">seconds
        </div>
        </div></span>
      <span data-js="countdown-endtext" class="countdown-endtext"></span>
    </div>
    <div class="container-width">
      <div class="footer-container">
        <div id="i8kohz" class="foot-lists">
          <div class="foot-list">
            <div class="foot-list-title">About us
            </div>
            <div class="foot-list-item">Contact
            </div>
            <div class="foot-list-item">Events
            </div>
            <div class="foot-list-item">Company
            </div>
            <div class="foot-list-item">Jobs
            </div>
            <div class="foot-list-item">Blog
            </div>
          </div>
          <div class="foot-list">
            <div class="foot-list-title">Services
            </div>
            <div id="itw23o" class="foot-list-item">Education
            </div>
            <div class="foot-list-item">Partner
            </div>
            <div id="iq9qp8" class="foot-list-item">Community
            </div>
            <div id="iclk6e" class="foot-list-item">Forum
            </div>
            <div id="i7ylag" class="foot-list-item">Download
            </div>
            <div class="foot-list-item">Upgrade
            </div>
          </div>
          <div class="clearfix">
          </div>
        </div>
        <div class="form-sub">
          <div class="foot-form-cont">
            <div class="foot-form-title">Subscribe
            </div>
            <div class="foot-form-desc">Subscribe to our newsletter to receive exclusive offers and the latest news
            </div>
            <input type="text" name="name" placeholder="Name" class="sub-input"/>
            <input type="text" name="email" placeholder="Email" class="sub-input"/>
            <button type="button" class="sub-btn">Submit</button>
          </div>
        </div>
      </div>
    </div>
    <div class="copyright">
      <div class="container-width">
        <div class="made-with">
          made with GrapesJS
        </div>
        <div class="foot-social-btns">facebook twitter linkedin mail
        </div>
        <div class="clearfix">
        </div>
      </div>
    </div>
  </footer>
</body>
<script>var props = {
    "iwrsax":{
      "classactive":"tab-active","selectortab":"aria-controls"}
  };
  var ids = Object.keys(props).map(function(id) {
    return '#'+id }
                                  ).join(',');
  var els = document.querySelectorAll(ids);
  for (var i = 0, len = els.length; i < len; i++) {
    var el = els[i];
    (function(t){
      var e,n,r=this,o=t.classactive,a=t.selectortab,c=window,i=c.history,s=c._isEditor,b='[role=tab]',p=document,l=p.body,u=p.location,f=l.matchesSelector||l.webkitMatchesSelector||l.mozMatchesSelector||l.msMatchesSelector,y=function(t,e){
        for(var n=t||[],r=0;r<n.length;r++)e(n[r],r)}
      ,d=function(t){
        return t.getAttribute(a)}
      ,O=function(t,e){
        return t.querySelector(e)}
      ,g=function(){
        return r.querySelectorAll(b)}
      ,j=function(t,e){
        return!s&&(t.tabIndex=e)}
      ,h=function(t){
        y(g(),(function(t){
          t.className=t.className.replace(o,'').trim(),t.ariaSelected='false',j(t,'-1')}
              )),y(r.querySelectorAll("[role=tabpanel]"),(function(t){
          return t.hidden=!0}
                                                         )),t.className+=' '+o,t.ariaSelected='true',j(t,'0');
        var e=d(t),n=e&&O(r,"#".concat(e));
        n&&(n.hidden=!1)}
      ,v=O(r,".".concat(o).concat(b));
      (v=v||(n=(u.hash||'').replace('#',''))&&O(r,(e=a,"".concat(b,"[").concat(e,"=").concat(n,"]")))||O(r,b))&&h(v),r.addEventListener('click',(function(t){
        var e=t.target,n=f.call(e,b);
        if(n||(e=function(t){
          var e;
          return y(g(),(function(n){
            e||n.contains(t)&&(e=n)}
                       )),e}
               (e))&&(n=1),n&&!t.__trg&&e.className.indexOf(o)<0){
          t.preventDefault(),t.__trg=1,h(e);
          var r=d(e);
          try{
            i&&i.pushState(null,null,"#".concat(r))}
          catch(t){
          }
        }
      }
                                                                                                                                                ))}
     .bind(el))(props[el.id]);
  }
  var props = {
    "i0wct6":{
      "startfrom":"","endText":"EXPIRED"}
  };
  var ids = Object.keys(props).map(function(id) {
    return '#'+id }
                                  ).join(',');
  var els = document.querySelectorAll(ids);
  for (var i = 0, len = els.length; i < len; i++) {
    var el = els[i];
    (function(n){
      var t=n.startfrom,e=n.endText,o=this,a=new Date(t).getTime(),c=o.querySelector('[data-js=countdown]'),d=o.querySelector('[data-js=countdown-endtext]'),s=o.querySelector('[data-js=countdown-day]'),l=o.querySelector('[data-js=countdown-hour]'),i=o.querySelector('[data-js=countdown-minute]'),r=o.querySelector('[data-js=countdown-second]'),u=o.__gjsCountdownInterval;
      u&&clearInterval(u);
      var p=window.__gjsCountdownIntervals||[],v=[];
      p.forEach((function(n){
        n.isConnected||(clearInterval(n.__gjsCountdownInterval),v.push(n))}
                )),p.indexOf(o)<0&&p.push(o),window.__gjsCountdownIntervals=p.filter((function(n){
        return v.indexOf(n)<0}
                                                                                     ));
      var y=function(n,t,e,o){
        s.innerHTML="".concat(n<10?'0'+n:n),l.innerHTML="".concat(t<10?'0'+t:t),i.innerHTML="".concat(e<10?'0'+e:e),r.innerHTML="".concat(o<10?'0'+o:o)}
      ,f=function(){
        var n=(new Date).getTime(),t=a-n,s=Math.floor(t/864e5),l=Math.floor(t%864e5/36e5),i=Math.floor(t%36e5/6e4),r=Math.floor(t%6e4/1e3);
        y(s,l,i,r),t<0&&(clearInterval(o.__gjsCountdownInterval),d.innerHTML=e,c.style.display='none',d.style.display='')};
      a?(o.__gjsCountdownInterval=setInterval(f,1e3),d.style.display='none',c.style.display='',f()):y(0,0,0,0)}
     .bind(el))(props[el.id]);
  }
</script>
<style>* {
  box-sizing: border-box;
  }
  body {
    margin: 0;
  }
  .clearfix{
    clear:both;
  }
  .header-banner{
    padding-top:35px;
    padding-bottom:100px;
    color:#ffffff;
    font-family:Helvetica, serif;
    font-weight:100;
    background-image:url("/assets/images/demos/bg-gr-v.png"), url("/assets/images/demos/work-desk.jpg");
    background-attachment:scroll, scroll;
    background-position:left top, center center;
    background-repeat:repeat-y, no-repeat;
    background-size:contain, cover;
  }
  .container-width{
    width:90%;
    max-width:1150px;
    margin:0 auto;
  }
  .logo-container{
    float:left;
    width:50%;
  }
  .logo{
    background-color:#fff;
    border-radius:5px;
    width:130px;
    padding:10px;
    min-height:30px;
    text-align:center;
    line-height:30px;
    color:#4d114f;
    font-size:23px;
  }
  .menu{
    float:right;
    width:50%;
  }
  .menu-item{
    float:right;
    font-size:15px;
    color:#eee;
    width:130px;
    padding:10px;
    min-height:50px;
    text-align:center;
    line-height:30px;
    font-weight:400;
  }
  .lead-title{
    margin:150px 0 30px 0;
    font-size:40px;
  }
  .sub-lead-title{
    max-width:650px;
    line-height:30px;
    margin-bottom:30px;
    color:#c6c6c6;
  }
  .lead-btn{
    margin-top:15px;
    padding:10px;
    width:190px;
    min-height:30px;
    font-size:20px;
    text-align:center;
    letter-spacing:3px;
    line-height:30px;
    background-color:#d983a6;
    border-radius:5px;
    transition:all 0.5s ease;
    cursor:pointer;
  }
  .lead-btn:hover{
    background-color:#ffffff;
    color:#4c114e;
  }
  .lead-btn:active{
    background-color:#4d114f;
    color:#fff;
  }
  .flex-sect{
    background-color:#fafafa;
    padding:100px 0;
    font-family:Helvetica, serif;
  }
  .flex-title{
    margin-bottom:15px;
    font-size:2em;
    text-align:center;
    font-weight:700;
    color:#555;
    padding:5px;
  }
  .flex-desc{
    margin-bottom:55px;
    font-size:1em;
    color:rgba(0, 0, 0, 0.5);
    text-align:center;
    padding:5px;
  }
  .cards{
    padding:20px 0;
    display:flex;
    justify-content:space-around;
    flex-flow:wrap;
  }
  .card{
    background-color:white;
    height:300px;
    width:300px;
    margin-bottom:30px;
    box-shadow:0 1px 2px 0 rgba(0, 0, 0, 0.2);
    border-radius:2px;
    transition:all 0.5s ease;
    font-weight:100;
    overflow:hidden;
  }
  .card:hover{
    margin-top:-5px;
    box-shadow:0 20px 30px 0 rgba(0, 0, 0, 0.2);
  }
  .card-header{
    height:155px;
    background-image:url("https://via.placeholder.com/350x250/78c5d6/fff");
    background-size:cover;
    background-position:center center;
  }
  .card-header.ch2{
    background-image:url("https://via.placeholder.com/350x250/459ba8/fff");
  }
  .card-header.ch3{
    background-image:url("https://via.placeholder.com/350x250/79c267/fff");
  }
  .card-header.ch4{
    background-image:url("https://via.placeholder.com/350x250/c5d647/fff");
  }
  .card-header.ch5{
    background-image:url("https://via.placeholder.com/350x250/f28c33/fff");
  }
  .card-header.ch6{
    background-image:url("https://via.placeholder.com/350x250/e868a2/fff");
  }
  .card-body{
    padding:15px 15px 5px 15px;
    color:#555;
  }
  .card-title{
    font-size:1.4em;
    margin-bottom:5px;
  }
  .card-sub-title{
    color:#b3b3b3;
    font-size:1em;
    margin-bottom:15px;
  }
  .card-desc{
    font-size:0.85rem;
    line-height:17px;
  }
  .am-sect{
    padding-top:100px;
    padding-bottom:100px;
    font-family:Helvetica, serif;
  }
  .img-phone{
    float:left;
  }
  .am-container{
    display:flex;
    flex-wrap:wrap;
    align-items:center;
    justify-content:space-around;
  }
  .am-content{
    float:left;
    padding:7px;
    width:490px;
    color:#444;
    font-weight:100;
    margin-top:50px;
  }
  .am-pre{
    padding:7px;
    color:#b1b1b1;
    font-size:15px;
  }
  .am-title{
    padding:7px;
    font-size:25px;
    font-weight:400;
  }
  .am-desc{
    padding:7px;
    font-size:17px;
    line-height:25px;
  }
  .am-post{
    padding:7px;
    line-height:25px;
    font-size:13px;
  }
  .blk-sect{
    padding-top:100px;
    padding-bottom:100px;
    background-color:#222222;
    font-family:Helvetica, serif;
  }
  .blk-title{
    color:#fff;
    font-size:25px;
    text-align:center;
    margin-bottom:15px;
  }
  .blk-desc{
    color:#b1b1b1;
    font-size:15px;
    text-align:center;
    max-width:700px;
    margin:0 auto;
    font-weight:100;
  }
  .price-cards{
    margin-top:70px;
    display:flex;
    flex-wrap:wrap;
    align-items:center;
    justify-content:space-around;
  }
  .price-card-cont{
    width:300px;
    padding:7px;
    float:left;
  }
  .price-card{
    margin:0 auto;
    min-height:350px;
    background-color:#d983a6;
    border-radius:5px;
    font-weight:100;
    color:#fff;
    width:90%;
  }
  .pc-title{
    font-weight:100;
    letter-spacing:3px;
    text-align:center;
    font-size:25px;
    background-color:rgba(0, 0, 0, 0.1);
    padding:20px;
  }
  .pc-desc{
    padding:75px 0;
    text-align:center;
  }
  .pc-feature{
    color:rgba(255,255,255,0.5);
    background-color:rgba(0, 0, 0, 0.1);
    letter-spacing:2px;
    font-size:15px;
    padding:10px 20px;
  }
  .pc-feature:nth-of-type(2n){
    background-color:transparent;
  }
  .pc-amount{
    background-color:rgba(0, 0, 0, 0.1);
    font-size:35px;
    text-align:center;
    padding:35px 0;
  }
  .pc-regular{
    background-color:#da78a0;
  }
  .pc-enterprise{
    background-color:#d66a96;
  }
  .footer-under{
    background-color:#312833;
    padding-bottom:100px;
    padding-top:100px;
    min-height:500px;
    color:#eee;
    position:relative;
    font-weight:100;
    font-family:Helvetica,serif;
  }
  .copyright{
    background-color:rgba(0, 0, 0, 0.15);
    color:rgba(238, 238, 238, 0.5);
    bottom:0;
    padding:1em 0;
    position:absolute;
    width:100%;
    font-size:0.75em;
  }
  .made-with{
    float:left;
    width:50%;
    padding:5px 0;
  }
  .foot-social-btns{
    display:none;
    float:right;
    width:50%;
    text-align:right;
    padding:5px 0;
  }
  .footer-container{
    display:flex;
    flex-wrap:wrap;
    align-items:stretch;
    justify-content:space-around;
  }
  .foot-list{
    float:left;
    width:200px;
  }
  .foot-list-title{
    font-weight:400;
    margin-bottom:10px;
    padding:0.5em 0;
  }
  .foot-list-item{
    color:rgba(238, 238, 238, 0.8);
    font-size:0.8em;
    padding:0.5em 0;
  }
  .foot-list-item:hover{
    color:rgba(238, 238, 238, 1);
  }
  .foot-form-cont{
    width:300px;
    float:right;
  }
  .foot-form-title{
    color:rgba(255,255,255,0.75);
    font-weight:400;
    margin-bottom:10px;
    padding:0.5em 0;
    text-align:right;
    font-size:2em;
  }
  .foot-form-desc{
    font-size:0.8em;
    color:rgba(255,255,255,0.55);
    line-height:20px;
    text-align:right;
    margin-bottom:15px;
  }
  .sub-input{
    width:100%;
    margin-bottom:15px;
    padding:7px 10px;
    border-radius:2px;
    color:#fff;
    background-color:#554c57;
    border:none;
  }
  .sub-btn{
    width:100%;
    margin:15px 0;
    background-color:#785580;
    border:none;
    color:#fff;
    border-radius:2px;
    padding:7px 10px;
    font-size:1em;
    cursor:pointer;
  }
  .sub-btn:hover{
    background-color:#91699a;
  }
  .sub-btn:active{
    background-color:#573f5c;
  }
  .bdg-sect{
    padding-top:100px;
    padding-bottom:100px;
    font-family:Helvetica, serif;
    background-color:#fafafa;
  }
  .bdg-title{
    text-align:center;
    font-size:2em;
    margin-bottom:55px;
    color:#555555;
  }
  .badges{
    padding:20px;
    display:flex;
    justify-content:space-around;
    align-items:flex-start;
    flex-wrap:wrap;
  }
  .badge{
    width:290px;
    font-family:Helvetica, serif;
    background-color:white;
    margin-bottom:30px;
    box-shadow:0 2px 2px 0 rgba(0, 0, 0, 0.2);
    border-radius:3px;
    font-weight:100;
    overflow:hidden;
    text-align:center;
  }
  .badge-header{
    height:115px;
    background-image:url("/assets/images/demos/bg-gr-v.png"), url("/assets/images/demos/work-desk.jpg");
    background-position:left top, center center;
    background-attachment:scroll, fixed;
    overflow:hidden;
  }
  .badge-name{
    font-size:1.4em;
    margin-bottom:5px;
  }
  .badge-role{
    color:#777;
    font-size:1em;
    margin-bottom:25px;
  }
  .badge-desc{
    font-size:0.85rem;
    line-height:20px;
  }
  .badge-avatar{
    width:100px;
    height:100px;
    border-radius:100%;
    border:5px solid #fff;
    box-shadow:0 1px 1px 0 rgba(0, 0, 0, 0.2);
    margin-top:-75px;
    position:relative;
  }
  .badge-body{
    margin:35px 10px;
  }
  .badge-foot{
    color:#fff;
    background-color:#a290a5;
    padding-top:13px;
    padding-bottom:13px;
    display:flex;
    justify-content:center;
  }
  .badge-link{
    height:35px;
    width:35px;
    line-height:35px;
    font-weight:700;
    background-color:#fff;
    color:#a290a5;
    display:block;
    border-radius:100%;
    margin:0 10px;
  }
  .tooltip-component{
    position:relative;
    display:inline-block;
    vertical-align:top;
  }
  .tooltip-component--empty{
    width:50px;
    height:50px;
  }
  .tooltip-component__body, [data-tooltip]::after{
    font-family:Helvetica, sans-serif;
    background:rgba(55, 61, 73, 0.95);
    border-radius:3px;
    bottom:100%;
    color:#fff;
    content:attr(data-tooltip);
    display:block;
    font-size:12px;
    left:50%;
    line-height:normal;
    max-width:32rem;
    opacity:0;
    overflow:hidden;
    padding:8px 16px;
    pointer-events:none;
    position:absolute;
    text-overflow:ellipsis;
    transform:translate(-50%, 0);
    transition:opacity 0.25s, transform 0.25s;
    white-space:nowrap;
    box-sizing:border-box;
    z-index:10;
  }
  [data-tooltip-visible=true]::after, [data-tooltip]:focus::after, [data-tooltip]:hover::after{
    opacity:1;
    transform:translate(-50%, -0.5rem);
  }
  [data-tooltip-pos=right]::after{
    bottom:50%;
    left:100%;
    transform:translate(0, 50%);
  }
  [data-tooltip-pos=right]:focus::after, [data-tooltip-pos=right]:hover::after, [data-tooltip-visible=true][data-tooltip-pos=right]::after{
    transform:translate(0.5rem, 50%);
  }
  [data-tooltip-pos=bottom]::after{
    bottom:auto;
    top:100%;
    transform:translate(-50%, 0);
  }
  [data-tooltip-pos=bottom]:focus::after, [data-tooltip-pos=bottom]:hover::after, [data-tooltip-visible=true][data-tooltip-pos=bottom]::after{
    transform:translate(-50%, 0.5rem);
  }
  [data-tooltip-pos=left]::after{
    bottom:50%;
    left:auto;
    right:100%;
    transform:translate(0, 50%);
  }
  [data-tooltip-pos=left]:focus::after, [data-tooltip-pos=left]:hover::after, [data-tooltip-visible=true][data-tooltip-pos=left]::after{
    transform:translate(-0.5rem, 50%);
  }
  [data-tooltip-length=small]::after{
    white-space:normal;
    width:80px;
  }
  [data-tooltip-length=medium]::after{
    white-space:normal;
    width:150px;
  }
  [data-tooltip-length=large]::after{
    white-space:normal;
    width:300px;
  }
  [data-tooltip-length=fit]::after{
    white-space:normal;
    width:100%;
  }
  // IE 11 bugfix
  button[data-tooltip]{
    overflow:visible;
  }
  #icl1{
    text-align:justify;
    color:#ffffff;
  }
  .countdown{
    text-align:center;
  }
  .countdown-block{
    display:inline-block;
    margin:0 10px;
    padding:10px;
  }
  .countdown-digit{
    font-size:5rem;
  }
  .countdown-endtext{
    font-size:5rem;
  }
  .countdown-cont{
    display:inline-block;
  }
  @media (max-width: 768px){
    .foot-form-cont{
      width:400px;
    }
    .foot-form-title{
      width:autopx;
    }
  }
  @media (max-width: 480px){
    .foot-lists{
      display:none;
    }
  }
</style>