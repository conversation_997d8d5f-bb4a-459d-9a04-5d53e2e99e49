.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
  box-sizing: border-box;
  background-color: #f8f8f8;
}

.camera {
  width: 600rpx;
  height: 800rpx;
  border-radius: 20rpx;
  overflow: hidden;
  margin-bottom: 40rpx;
}

.status-area {
  width: 100%;
  padding: 30rpx;
  box-sizing: border-box;
  text-align: center;
}

.status-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.timer {
  font-size: 48rpx;
  color: #07c160;
  font-weight: bold;
  margin: 20rpx 0;
}

.interval-text {
  font-size: 28rpx;
  color: #666;
  margin: 20rpx 0;
}

.button-area {
  margin-top: 40rpx;
  width: 100%;
  display: flex;
  justify-content: center;
}

.btn {
  width: 80%;
  margin: 0;
}

.error-tips {
  width: 80%;
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  border-radius: 8rpx;
  padding: 20rpx;
  margin: 20rpx 0;
}