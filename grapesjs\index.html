<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <title>GrapesJS 完整功能演示</title>
    <link rel="stylesheet" href="./libs/grapes.min.css" />
    <script src="./libs/grapes.min.js"></script>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>

    <style>
      body {
        margin: 0;
        padding: 0;
      }

      .carousel-container {
        position: relative;
        width: 100%;
        height: 400px;
        overflow: hidden;
      }

      .carousel-slides {
        display: flex;
        transition: transform 0.5s ease;
        height: 100%;
      }

      .carousel-slide {
        min-width: 100%;
        height: 100%;
      }

      .carousel-slide img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .carousel-controls {
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        display: flex;
        gap: 10px;
      }

      .carousel-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.5);
        cursor: pointer;
      }

      .carousel-dot.active {
        background: white;
      }

      .image-list {
        max-height: 300px;
        overflow-y: auto;
      }

      .image-item {
        display: flex;
        align-items: center;
        padding: 8px;
        border: 1px solid #ddd;
        margin-bottom: 8px;
        cursor: move;
      }

      .image-item img {
        width: 60px;
        height: 60px;
        object-fit: cover;
        margin-right: 10px;
      }

      .image-item .delete-btn {
        margin-left: auto;
        color: red;
        cursor: pointer;
      }

      .drop-zone {
        border: 2px dashed #ccc;
        padding: 20px;
        text-align: center;
        margin-bottom: 20px;
      }

      .drop-zone.dragover {
        background: #f0f0f0;
        border-color: #999;
      }
    </style>
  </head>
  <body>
    <div id="gjs"> 
      <h1>标题123</h1>
      <p>内容123123</p>
    </div>
    <script type="module">
      window.onload = () => {
        const editor = grapesjs.init({
          container: '#gjs',
          height: '100vh',
          width: '100%',
          fromElement: true,
          assetManager: {
            upload: true,
            uploadName: 'files',
            uploadText: 'Drop files here or click to upload',
            modalTitle: 'Select Image',
            addBtnText: 'Add image',
            uploadText: 'Drop files here or click to upload',
            dropzone: true,
            dropzoneContent: 'Drop files here',
          },
          allowScripts: true,
          storageManager: false,
          plugins: [],
          pluginsOpts: {},
          canvas: {
            scripts: [
              'https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js',
            ],
            styles: ['https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css'],
          },
          styleManager: {
            // appendTo: '.styles-container',
            sectors: [
              {
                // name: 'Dimension',
                open: false,
                // Use built-in properties
                buildProps: ['width', 'min-height', 'padding'],
                // Use `properties` to define/override single property
                properties: [
                  {
                    // Type of the input,
                    // options: integer | radio | select | color | slider | file | composite | stack
                    type: 'slider',
                    name: '宽度', // Label for the property
                    property: 'width', // CSS property (if buildProps contains it will be extended)
                    units: ['px', '%'], // Units, available only for 'integer' types
                    defaults: 'auto', // Default value
                    min: 0, // Min value, available only for 'integer' types
                  },
                ],
              },
              {
                // name: 'Extra',
                open: false,
                buildProps: ['background-color', 'box-shadow', 'custom-prop'],
                properties: [
                  {
                    id: 'custom-prop',
                    name: 'Custom Label',
                    property: 'font-size',
                    type: 'select',
                    defaults: '32px',
                    // List of options, available only for 'select' and 'radio'  types
                    options: [
                      { value: '12px', name: 'Tiny' },
                      { value: '18px', name: 'Medium' },
                      { value: '32px', name: 'Big' },
                    ],
                  },
                ],
              },
            ],
          },
        });

        // 添加轮播图组件
        editor.DomComponents.addType('carousel', {
          model: {
            defaults: {
              tagName: 'div',
              droppable: false,
              attributes: { class: 'carousel carousel-dark slide' },
              traits: [
                {
                  type: 'text',
                  name: 'imgList',
                  label: '图片列表',
                  default: '[]',
                },
              ],
              components: [
                {
                  type: 'div',
                  classes: 'carousel-inner',
                },
                {
                  type: 'div',
                  classes: 'carousel-indicators',
                },
                {
                  type: 'button',
                  classes: 'carousel-control-prev',
                  attributes: {
                    type: 'button',
                    'data-bs-slide': 'prev',
                  },
                  components: [
                    {
                      type: 'span',
                      classes: 'carousel-control-prev-icon',
                      attributes: {
                        'aria-hidden': 'true',
                      },
                    },
                  ],
                },
                {
                  type: 'button',
                  classes: 'carousel-control-next',
                  attributes: {
                    type: 'button',
                    'data-bs-slide': 'next',
                  },
                  components: [
                    {
                      type: 'span',
                      classes: 'carousel-control-next-icon',
                      attributes: {
                        'aria-hidden': 'true',
                      },
                    },
                  ],
                },
              ],
              script: function () {
                const el = this;
                const slides = el.querySelector('.carousel-inner');
                const dots = el.querySelector('.carousel-indicators');
                let currentSlide = 0;
                let imgList = el.getAttribute('imgList').split(',') || [];
                let slideInterval;

                function updateSlides() {
                  slides.innerHTML = imgList
                    .map(
                      (img, index) => `
                      <div class="carousel-item ${
                        index === 0 ? 'active' : ''
                      }" data-bs-interval="3000">
                        <img src="${img}" class="d-block w-100" alt="slide">
                      </div>
                    `,
                    )
                    .join('');
                }

                function updateDots() {
                  dots.innerHTML = imgList
                    .map(
                      (_, i) => `
                      <button type="button"
                        data-bs-target="#${el.id}"
                        data-bs-slide-to="${i}"
                        class="${i === 0 ? 'active' : ''}"
                        aria-label="Slide ${i + 1}"
                        ${i === 0 ? 'aria-current="true"' : ''}>
                      </button>
                    `,
                    )
                    .join('');
                }

                function showSlide(index) {
                  currentSlide = index;
                  const items = slides.querySelectorAll('.carousel-item');
                  items.forEach((item, i) => {
                    item.classList.toggle('active', i === index);
                  });
                  const indicators = dots.querySelectorAll('button');
                  indicators.forEach((indicator, i) => {
                    indicator.classList.toggle('active', i === index);
                    indicator.setAttribute('aria-current', i === index ? 'true' : 'false');
                  });
                }

                function nextSlide() {
                  currentSlide = (currentSlide + 1) % imgList.length;
                  showSlide(currentSlide);
                }

                function initCarousel() {
                  updateSlides();
                  updateDots();
                  if (slideInterval) {
                    clearInterval(slideInterval);
                  }
                  slideInterval = setInterval(nextSlide, 3000);
                }

                // 初始化
                initCarousel();

                // 点击指示器切换
                dots.addEventListener('click', (e) => {
                  const button = e.target.closest('button');
                  if (button) {
                    const index = parseInt(button.getAttribute('data-bs-slide-to'));
                    showSlide(index);
                  }
                });

                // 添加前进后退按钮事件
                const prevBtn = el.querySelector('.carousel-control-prev');
                const nextBtn = el.querySelector('.carousel-control-next');

                prevBtn.addEventListener('click', () => {
                  currentSlide = (currentSlide - 1 + imgList.length) % imgList.length;
                  showSlide(currentSlide);
                });

                nextBtn.addEventListener('click', () => {
                  currentSlide = (currentSlide + 1) % imgList.length;
                  showSlide(currentSlide);
                });

                // 将方法暴露给组件实例
                el.updateCarousel = function (newImgList) {
                  imgList = newImgList;
                  initCarousel();
                };
              },
            },
            init() {
              this.on('change:attributes:imgList', this.handleImgListChange);
            },
            handleImgListChange() {
              const imgList = this.getAttributes().imgList.split(',') || [];
              console.log('imgList: ', imgList);

              // 获取组件实例并调用更新方法
              const component = this.getEl();
              if (component && component.updateCarousel) {
                component.updateCarousel(imgList);
              }
            },
          },
          view: {
            events: {
              dblclick: 'onActive',
            },
            onActive() {
              const model = this.model;
              const imgList = model.getAttributes().imgList.split(',') || [];
              console.log('imgList: ', imgList);

              const modal = document.createElement('div');
              modal.className = 'modal fade';
              modal.innerHTML = `
                  <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                      <div class="modal-header">
                        <h5 class="modal-title">轮播图设置</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                      </div>
                      <div class="modal-body">
                        <div class="row">
                          <div class="col-md-6">
                            <div class="drop-zone" id="dropZone">
                              <p>拖拽图片到这里或点击上传</p>
                              <input type="file" id="fileInput" multiple accept="image/*" style="display: none">
                            </div>
                          </div>
                          <div class="col-md-6">
                            <div class="image-list" id="imageList"></div>
                          </div>
                        </div>
                      </div>
                      <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" id="saveBtn">保存</button>
                      </div>
                    </div>
                  </div>
                `;

              document.body.appendChild(modal);
              const modalInstance = new bootstrap.Modal(modal);
              modalInstance.show();

              const dropZone = modal.querySelector('#dropZone');
              const fileInput = modal.querySelector('#fileInput');
              const imageList = modal.querySelector('#imageList');
              const saveBtn = modal.querySelector('#saveBtn');

              // 初始化图片列表
              function updateImageList() {
                imageList.innerHTML = imgList
                  .map(
                    (img, index) => `
                    <div class="image-item" data-index="${index}">
                      <img src="${img}" alt="slide">
                      <span class="delete-btn">×</span>
                    </div>
                  `,
                  )
                  .join('');
              }
              updateImageList();

              // 拖拽排序
              new Sortable(imageList, {
                animation: 150,
                onEnd: function (evt) {
                  const item = imgList.splice(evt.oldIndex, 1)[0];
                  imgList.splice(evt.newIndex, 0, item);
                  updateImageList();
                },
              });

              // 删除图片
              imageList.addEventListener('click', (e) => {
                if (e.target.classList.contains('delete-btn')) {
                  const index = e.target.closest('.image-item').dataset.index;
                  imgList.splice(index, 1);
                  updateImageList();
                }
              });

              // 文件上传
              dropZone.addEventListener('click', () => fileInput.click());
              dropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropZone.classList.add('dragover');
              });
              dropZone.addEventListener('dragleave', () => {
                dropZone.classList.remove('dragover');
              });
              dropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                dropZone.classList.remove('dragover');
                handleFiles(e.dataTransfer.files);
              });
              fileInput.addEventListener('change', (e) => {
                handleFiles(e.target.files);
              });

              function handleFiles(files) {
                Array.from(files).forEach((file) => {
                  if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                      imgList.push(e.target.result);
                      updateImageList();
                    };
                    reader.readAsDataURL(file);
                  }
                });
              }

              // 保存
              saveBtn.addEventListener('click', () => {
                model.setAttributes({ imgList: imgList.join(',') });
                modalInstance.hide();
                modal.remove();
              });

              // 关闭模态框时清理
              modal.addEventListener('hidden.bs.modal', () => {
                modal.remove();
              });
            },
          },
        });

        // 添加轮播图块
        editor.BlockManager.add('carousel', {
          label: '轮播图',
          category: 'Basic',
          content: {
            type: 'carousel',
            attributes: {
              imgList:
                'https://dummyimage.com/1200x300&text=1,https://dummyimage.com/1200x300&text=2,https://dummyimage.com/1200x300&text=3',
            },
          },
        });

        // 添加飘窗组件
        editor.DomComponents.addType('floating-window', {
          model: {
            defaults: {
              tagName: 'div',
              droppable: false,
              attributes: { class: 'floating-window' },
              style: {
                width: '200px',
                position: 'fixed',
                cursor: 'pointer',
                'z-index': 9999,
              },
              styles: `
                  .floating-window {
                  }
                `,
              traits: [
                {
                  type: 'text',
                  name: 'imageUrl',
                  label: '图片地址',
                  default: 'https://dummyimage.com/200x200&text=Image',
                },
                {
                  type: 'number',
                  name: 'speed',
                  label: '移动速度',
                  default: 0.1,
                  min: 1,
                  max: 10,
                },
                {
                  type: 'number',
                  name: 'angle',
                  label: '移动角度',
                  default: 45,
                  min: 0,
                  max: 360,
                },
              ],
              script: function () {
                const el = this;
                const elHeight = el.offsetHeight;
                let posX = Math.random() * (window.innerWidth - 200);
                let posY = Math.random() * (window.innerHeight - elHeight);
                let speed = parseFloat(el.getAttribute('speed')) || 2;
                let angle = parseFloat(el.getAttribute('angle')) || 45;
                let isHovered = false;
                let animationId;

                function updatePosition() {
                  if (isHovered) return;

                  const radians = (angle * Math.PI) / 180;
                  posX += Math.cos(radians) * speed;
                  posY += Math.sin(radians) * speed;

                  // 碰到边界反弹
                  if (posX <= 0 || posX >= window.innerWidth - 200) {
                    angle = 180 - angle;
                  }
                  if (posY <= 0 || posY >= window.innerHeight - elHeight) {
                    angle = 360 - angle;
                  }

                  el.style.left = posX + 'px';
                  el.style.top = posY + 'px';
                  animationId = requestAnimationFrame(updatePosition);
                }

                // 开始动画
                updatePosition();

                // 鼠标悬浮事件
                el.addEventListener('mouseenter', () => {
                  isHovered = true;
                  cancelAnimationFrame(animationId);
                });

                el.addEventListener('mouseleave', () => {
                  isHovered = false;
                  updatePosition();
                });
              },
            },
            init() {
              this.on('change:attributes:imageUrl', this.handleImageChange);
              this.on('change:attributes:speed', this.handleSpeedChange);
              this.on('change:attributes:angle', this.handleAngleChange);
            },
            handleImageChange() {
              const img = this.getEl().querySelector('img');
              if (img) {
                img.src = this.getAttributes().imageUrl;
              }
            },
            handleSpeedChange() {
              const el = this.getEl();
              if (el) {
                el.setAttribute('speed', this.getAttributes().speed);
              }
            },
            handleAngleChange() {
              const el = this.getEl();
              if (el) {
                el.setAttribute('angle', this.getAttributes().angle);
              }
            },
          },
          view: {
            events: {
              dblclick: 'onActive',
            },
            onActive() {
              const model = this.model;
              console.log('model: ', model);
              const modal = document.createElement('div');
              modal.className = 'modal fade';
              modal.innerHTML = `
                  <div class="modal-dialog">
                    <div class="modal-content">
                      <div class="modal-header">
                        <h5 class="modal-title">飘窗设置</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                      </div>
                      <div class="modal-body">
                        <div class="mb-3">
                          <label class="form-label">图片地址</label>
                          <div class="input-group">
                            <input type="text" class="form-control" id="imageUrl" value="${
                              model.getAttributes().imageUrl
                            }">
                            <button class="btn btn-outline-secondary" type="button" id="uploadBtn">上传</button>
                            <input type="file" id="fileInput" accept="image/*" style="display: none">
                          </div>
                          <div class="mt-2">
                            <img id="imagePreview" src="${
                              model.getAttributes().imageUrl
                            }" style="max-width: 100%; max-height: 200px; display: block; margin: 0 auto;">
                          </div>
                        </div>
                        <div class="mb-3">
                          <label class="form-label">移动速度</label>
                          <input type="number" class="form-control" id="speed" value="${
                            model.getAttributes().speed
                          }" min="1" max="10">
                        </div>
                        <div class="mb-3">
                          <label class="form-label">移动角度</label>
                          <input type="number" class="form-control" id="angle" value="${
                            model.getAttributes().angle
                          }" min="0" max="360">
                        </div>
                      </div>
                      <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" id="saveBtn">保存</button>
                      </div>
                    </div>
                  </div>
                `;

              document.body.appendChild(modal);
              const modalInstance = new bootstrap.Modal(modal);
              modalInstance.show();

              const imageUrl = modal.querySelector('#imageUrl');
              const imagePreview = modal.querySelector('#imagePreview');
              const uploadBtn = modal.querySelector('#uploadBtn');
              const fileInput = modal.querySelector('#fileInput');
              const saveBtn = modal.querySelector('#saveBtn');

              // 图片预览
              imageUrl.addEventListener('input', () => {
                imagePreview.src = imageUrl.value;
              });

              // 上传按钮点击事件
              uploadBtn.addEventListener('click', () => {
                fileInput.click();
              });

              // 文件选择事件
              fileInput.addEventListener('change', (e) => {
                const file = e.target.files[0];
                if (file) {
                  const reader = new FileReader();
                  reader.onload = (e) => {
                    imageUrl.value = e.target.result;
                    imagePreview.src = e.target.result;
                  };
                  reader.readAsDataURL(file);
                }
              });

              saveBtn.addEventListener('click', () => {
                const speed = modal.querySelector('#speed').value;
                const angle = modal.querySelector('#angle').value;
                model.setAttributes({
                  imageUrl: imageUrl.value,
                  speed,
                  angle,
                });
                this.updateScript();
                modalInstance.hide();
                modal.remove();
              });

              modal.addEventListener('hidden.bs.modal', () => {
                modal.remove();
              });
            },
          },
        });

        // 添加飘窗块
        editor.BlockManager.add('floating-window', {
          label: '飘窗',
          category: 'Basic',
          content: {
            type: 'floating-window',
            attributes: {
              imageUrl: 'https://dummyimage.com/200x200&text=Image',
              speed: 1,
              angle: 45,
            },
            components: [
              {
                type: 'image',
                // 禁止点击
                draggable: false,
                selectable: false,
                attributes: {
                  src: 'https://dummyimage.com/200x200&text=Image',
                },
                style: {
                  'pointer-events': 'none',
                  width: '100%',
                },
              },
            ],
          },
        });
      };
    </script>
  </body>
</html>
